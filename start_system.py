#!/usr/bin/env python3
"""
System Startup Script
Starts all components of the enhanced YOLO tracking system
"""

import os
import sys
import time
import subprocess
import threading
import signal
import logging
from pathlib import Path
from config import config_manager, config
from error_handling import health_checker, error_tracker

logger = logging.getLogger(__name__)

class SystemManager:
    """Manages system startup, monitoring, and shutdown"""
    
    def __init__(self):
        self.processes = {}
        self.running = True
        self.startup_complete = False
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.running = False
        self.shutdown()
        
    def check_dependencies(self) -> bool:
        """Check if all dependencies are available"""
        logger.info("Checking system dependencies...")
        
        # Check Redis
        try:
            import redis
            redis_client = redis.Redis.from_url(config_manager.get_redis_url())
            redis_client.ping()
            logger.info("✓ Redis connection successful")
        except Exception as e:
            logger.error(f"✗ Redis connection failed: {e}")
            return False
        
        # Check YOLO model
        model_path = config.yolo.model_path
        if not os.path.exists(model_path):
            logger.error(f"✗ YOLO model not found: {model_path}")
            return False
        logger.info(f"✓ YOLO model found: {model_path}")
        
        # Check directories
        config_manager.ensure_directories()
        logger.info("✓ Required directories created")
        
        return True
    
    def start_redis(self) -> bool:
        """Start Redis server if not running"""
        try:
            # Check if Redis is already running
            import redis
            redis_client = redis.Redis.from_url(config_manager.get_redis_url())
            redis_client.ping()
            logger.info("Redis already running")
            return True
        except:
            pass
        
        logger.info("Starting Redis server...")
        try:
            # Try to start Redis using docker-compose
            if os.path.exists("docker-compose.yml"):
                process = subprocess.Popen([
                    "docker-compose", "up", "-d", "redis"
                ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                
                self.processes["redis"] = process
                
                # Wait for Redis to be ready
                for _ in range(30):  # 30 second timeout
                    try:
                        import redis
                        redis_client = redis.Redis.from_url(config_manager.get_redis_url())
                        redis_client.ping()
                        logger.info("✓ Redis started successfully")
                        return True
                    except:
                        time.sleep(1)
                
                logger.error("Redis failed to start within timeout")
                return False
            else:
                logger.error("docker-compose.yml not found, cannot start Redis")
                return False
                
        except Exception as e:
            logger.error(f"Failed to start Redis: {e}")
            return False
    
    def start_celery_worker(self) -> bool:
        """Start Celery worker"""
        logger.info("Starting Celery worker...")
        try:
            cmd = [
                sys.executable, "-m", "celery",
                "-A", "celery_config",
                "worker",
                "--loglevel=info",
                "--concurrency=4",
                "--queues=high_priority,medium_priority,low_priority"
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=os.environ.copy()
            )
            
            self.processes["celery_worker"] = process
            logger.info("✓ Celery worker started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Celery worker: {e}")
            return False
    
    def start_celery_beat(self) -> bool:
        """Start Celery beat scheduler"""
        logger.info("Starting Celery beat...")
        try:
            cmd = [
                sys.executable, "-m", "celery",
                "-A", "celery_config",
                "beat",
                "--loglevel=info"
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=os.environ.copy()
            )
            
            self.processes["celery_beat"] = process
            logger.info("✓ Celery beat started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Celery beat: {e}")
            return False
    
    def start_flower(self) -> bool:
        """Start Flower monitoring"""
        logger.info("Starting Flower monitoring...")
        try:
            cmd = [
                sys.executable, "-m", "celery",
                "-A", "celery_config",
                "flower",
                "--port=5555"
            ]
            
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=os.environ.copy()
            )
            
            self.processes["flower"] = process
            logger.info("✓ Flower started on http://localhost:5555")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Flower: {e}")
            return False
    
    def start_gui(self) -> bool:
        """Start GUI application"""
        logger.info("Starting GUI application...")
        try:
            cmd = [sys.executable, "enhanced_gui_analyzer.py"]
            
            process = subprocess.Popen(
                cmd,
                env=os.environ.copy()
            )
            
            self.processes["gui"] = process
            logger.info("✓ GUI application started")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start GUI: {e}")
            return False
    
    def monitor_processes(self):
        """Monitor running processes and restart if needed"""
        while self.running:
            for name, process in list(self.processes.items()):
                if process.poll() is not None:  # Process has terminated
                    logger.warning(f"Process {name} has terminated")
                    
                    # Restart critical processes
                    if name in ["celery_worker", "redis"] and self.running:
                        logger.info(f"Restarting {name}...")
                        if name == "celery_worker":
                            self.start_celery_worker()
                        elif name == "redis":
                            self.start_redis()
            
            time.sleep(10)  # Check every 10 seconds
    
    def start_all(self, components: list = None) -> bool:
        """Start all system components"""
        if components is None:
            components = ["redis", "celery_worker", "celery_beat", "flower", "gui"]
        
        logger.info("Starting Enhanced YOLO Tracking System...")
        
        # Check dependencies first
        if not self.check_dependencies():
            logger.error("Dependency check failed")
            return False
        
        success = True
        
        # Start components in order
        if "redis" in components:
            success &= self.start_redis()
        
        if "celery_worker" in components and success:
            success &= self.start_celery_worker()
        
        if "celery_beat" in components and success:
            success &= self.start_celery_beat()
        
        if "flower" in components and success:
            success &= self.start_flower()
        
        if "gui" in components and success:
            success &= self.start_gui()
        
        if success:
            logger.info("✓ All components started successfully")
            self.startup_complete = True
            
            # Start monitoring thread
            monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
            monitor_thread.start()
            
            # Print status
            self.print_status()
            
        else:
            logger.error("✗ Some components failed to start")
            self.shutdown()
        
        return success
    
    def print_status(self):
        """Print system status"""
        print("\n" + "="*60)
        print("Enhanced YOLO Tracking System - Status")
        print("="*60)
        print(f"Redis: {'✓ Running' if 'redis' in self.processes else '✗ Not running'}")
        print(f"Celery Worker: {'✓ Running' if 'celery_worker' in self.processes else '✗ Not running'}")
        print(f"Celery Beat: {'✓ Running' if 'celery_beat' in self.processes else '✗ Not running'}")
        print(f"Flower Monitor: {'✓ Running' if 'flower' in self.processes else '✗ Not running'}")
        print(f"GUI Application: {'✓ Running' if 'gui' in self.processes else '✗ Not running'}")
        print("\nAccess Points:")
        print("- Flower Monitor: http://localhost:5555")
        print("- GUI Application: Desktop window")
        print("\nPress Ctrl+C to shutdown")
        print("="*60)
    
    def shutdown(self):
        """Shutdown all processes"""
        logger.info("Shutting down system...")
        
        for name, process in self.processes.items():
            try:
                logger.info(f"Stopping {name}...")
                process.terminate()
                
                # Wait for graceful shutdown
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    logger.warning(f"Force killing {name}...")
                    process.kill()
                    
            except Exception as e:
                logger.error(f"Error stopping {name}: {e}")
        
        self.processes.clear()
        logger.info("System shutdown complete")
    
    def wait(self):
        """Wait for system to run"""
        try:
            while self.running and self.processes:
                time.sleep(1)
        except KeyboardInterrupt:
            pass
        finally:
            self.shutdown()

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Enhanced YOLO Tracking System")
    parser.add_argument("--components", nargs="+", 
                       choices=["redis", "celery_worker", "celery_beat", "flower", "gui"],
                       help="Components to start (default: all)")
    parser.add_argument("--config", help="Configuration file path")
    parser.add_argument("--env", help="Environment (development/production)")
    
    args = parser.parse_args()
    
    # Set environment
    if args.env:
        os.environ["YOLO_ENV"] = args.env
    
    # Initialize system manager
    manager = SystemManager()
    
    # Start system
    if manager.start_all(args.components):
        manager.wait()
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
