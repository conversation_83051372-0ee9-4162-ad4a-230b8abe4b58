# Enhanced Real-Time YOLO Tracking & Possession Analysis System

This project provides a comprehensive, production-ready real-time system for YOLO-based object tracking with advanced visualization features, possession analytics, and robust distributed processing using Celery and Redis.

## 🚀 Key Features

### ⚽ Live Possession Analytics (NEW)
- **Real-time possession percentage calculation** with team-based tracking
- **Possession change detection** and counting with configurable thresholds
- **Live possession timeline visualization** showing possession flow over time
- **Team zone analysis** with customizable field division
- **Possession statistics** including changes, duration, and team performance

### 🎥 Enhanced Video Processing
- **YOLOv11 object detection** with configurable confidence thresholds
- **DeepSort tracking** for consistent player ID assignment
- **Real-time video display** with bounding boxes and player IDs
- **Quality settings** (Low/Medium/High) for performance optimization
- **Asynchronous processing** using Celery task queues

### 🔥 Advanced Heatmap Analysis
- **Dynamic heatmap generation** showing player activity zones
- **Team-separated views** (Team A, Team B, Combined, Individual)
- **Real-time updates** with configurable intensity scaling
- **Gaussian interpolation** for smooth visualization
- **Export capabilities** with high-resolution output

### 🦅 <PERSON>'s Eye View Tracking
- **Top-down field visualization** with proper proportions
- **Real-time movement trails** with configurable length
- **Team color coding** for easy identification
- **Possession zone overlays** showing field control areas
- **Interactive controls** for trail length and visibility

### 📊 Comprehensive Statistics
- **Live player statistics** including position, distance, and speed
- **Team-based analytics** and performance comparisons
- **System performance monitoring** with resource usage tracking
- **Error tracking and analysis** with categorized error reporting
- **Real-time updates** in tabular and graphical formats

### 🛠️ Robust System Architecture
- **Celery distributed processing** with priority-based task queues
- **Redis caching and data sharing** for real-time performance
- **Circuit breaker pattern** for system stability
- **Automatic error recovery** with exponential backoff
- **Health monitoring** with automatic system checks

### 🐳 Production Deployment
- **Docker containerization** with docker-compose orchestration
- **Environment-specific configurations** (development/production/testing)
- **Scalable worker deployment** with load balancing
- **Monitoring dashboard** with Flower integration
- **Automated startup scripts** for easy deployment

## 📋 System Requirements

### Minimum Requirements
- **Python**: 3.8 or higher
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 2GB free space
- **OS**: Windows, macOS, or Linux

### Recommended for Production
- **RAM**: 16GB or higher
- **GPU**: NVIDIA GPU with CUDA support (optional but recommended)
- **CPU**: Multi-core processor (4+ cores)
- **Network**: Stable internet connection for model downloads

## 🚀 Quick Start

### Option 1: Automated Setup (Recommended)
```bash
# Clone the repository
git clone <repository-url>
cd sena_project

# Make startup script executable (Linux/macOS)
chmod +x start.sh

# Start the complete system
./start.sh --install --env development

# Or for production with Docker
./start.sh --docker --env production
```

### Option 2: Manual Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Start Redis (if not using Docker)
redis-server

# Start Celery worker
celery -A celery_config worker --loglevel=info

# Start Celery beat scheduler
celery -A celery_config beat --loglevel=info

# Start monitoring (optional)
celery -A celery_config flower --port=5555

# Start the GUI application
python enhanced_gui_analyzer.py
```

### Option 3: Docker Deployment
```bash
# Start all services with Docker
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## 🎮 Usage Guide

### Main Interface Tabs

1. **Live Video**: Real-time video processing with detection overlays
2. **Possession Analytics**: Live possession statistics and timeline
3. **Heatmap Analysis**: Player activity heat zones
4. **Bird's Eye View**: Top-down field tracking
5. **Live Statistics**: Player and system performance metrics
6. **System Monitor**: Task queue and system health status

### Key Controls

- **Load Video**: Select video file for analysis
- **Play/Pause**: Control video playback
- **Quality Settings**: Adjust processing quality (Low/Medium/High)
- **Detection Threshold**: Configure YOLO confidence threshold
- **Trail Length**: Adjust movement trail length (5-100 frames)
- **Timeline Range**: Set possession timeline view (30s/1m/2m/5m)

### Possession Analytics Features

- **Team Possession Percentages**: Real-time possession distribution
- **Possession Changes**: Count of possession transitions
- **Current Possession**: Live possession status
- **Timeline Visualization**: Possession flow over time
- **Zone Analysis**: Field area control statistics

## 🔧 Configuration

### Environment Variables
```bash
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_password

# System Configuration
YOLO_ENV=development  # development/production/testing
YOLO_MODEL_PATH=yolo11n.pt
YOLO_CONFIDENCE=0.4
DEBUG=true
```

### Configuration Files
- `config.json`: Environment-specific settings
- `docker-compose.yml`: Container orchestration
- `celery_config.py`: Task queue configuration

## 📊 Monitoring and Debugging

### Access Points
- **Flower Monitor**: http://localhost:5555 (Celery task monitoring)
- **GUI Application**: Desktop window
- **System Logs**: `logs/` directory
- **Redis CLI**: `redis-cli` for data inspection

### Health Checks
```bash
# Test system components
python test_system.py

# Run specific tests
python test_system.py --test TestRedisConnectivity

# Performance benchmarks
python test_system.py --benchmark
```

## 📁 Project Structure

```
sena_project/
├── enhanced_gui_analyzer.py      # Main GUI application
├── celery_config.py              # Celery configuration
├── config.py                     # Configuration management
├── error_handling.py             # Error handling and recovery
├── start_system.py               # System startup manager
├── test_system.py                # Comprehensive test suite
├── yolo_tracker/                 # Task processing package
│   ├── __init__.py
│   └── tasks.py                  # Celery tasks
├── config.json                   # Environment configurations
├── docker-compose.yml            # Docker orchestration
├── Dockerfile                    # Container definition
├── requirements.txt              # Python dependencies
├── start.sh                      # Startup script
└── README_ENHANCED.md            # This file
```

## 🔍 Troubleshooting

### Common Issues

1. **Redis Connection Failed**
   ```bash
   # Start Redis manually
   redis-server
   # Or use Docker
   docker run -d -p 6379:6379 redis:alpine
   ```

2. **YOLO Model Not Found**
   ```bash
   # Download YOLOv11 model
   wget https://github.com/ultralytics/assets/releases/download/v0.0.0/yolo11n.pt
   ```

3. **Celery Workers Not Starting**
   ```bash
   # Check Redis connectivity
   redis-cli ping
   # Restart workers
   celery -A celery_config worker --loglevel=debug
   ```

4. **GUI Not Responding**
   - Check system resources (RAM/CPU usage)
   - Reduce video quality settings
   - Restart the application

### Performance Optimization

- **For High-Resolution Videos**: Use "Medium" or "Low" quality settings
- **For Multiple Videos**: Increase Celery worker concurrency
- **For Limited RAM**: Reduce trail length and heatmap resolution
- **For Better Accuracy**: Increase YOLO confidence threshold

## 🧪 Testing

```bash
# Run all tests
python test_system.py

# Run specific test categories
python test_system.py --integration
python test_system.py --benchmark

# Test specific components
python test_system.py --test TestPossessionAnalysis
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add comprehensive tests
4. Update documentation
5. Submit a pull request

## 📄 License

This project is for educational and research purposes. Please ensure you have appropriate rights for any video content used.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Review system logs in `logs/` directory
3. Run the test suite to identify issues
4. Check Flower monitor for task queue status
