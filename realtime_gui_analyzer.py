import cv2
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import threading
import time
from collections import defaultdict, deque
from ultralytics import YOLO
from deep_sort_realtime.deepsort_tracker import DeepSort
import json
from datetime import datetime

class RealTimeAnalyzerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Real-Time YOLO Tracking & Analysis GUI")
        self.root.geometry("1400x900")
        
        # Initialize models
        self.model = YOLO("yolo11n.pt")
        self.tracker = DeepSort(max_age=60, n_init=3, nms_max_overlap=1.0, embedder="mobilenet", half=True)
        
        # Video and tracking variables
        self.cap = None
        self.is_playing = False
        self.current_frame = None
        self.frame_count = 0
        self.tracking_data = defaultdict(list)
        self.heatmap_data = defaultdict(lambda: np.zeros((60, 100)))  # Field grid for heatmap
        self.recent_positions = defaultdict(lambda: deque(maxlen=30))  # Last 30 positions for trails
        
        # Video properties
        self.fps = 30
        self.width = 640
        self.height = 480
        
        self.setup_gui()
        
    def setup_gui(self):
        # Create main frames
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Control buttons
        ttk.Button(control_frame, text="Load Video", command=self.load_video).pack(side=tk.LEFT, padx=5)
        self.play_button = ttk.Button(control_frame, text="Play", command=self.toggle_playback)
        self.play_button.pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Reset", command=self.reset_analysis).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Save Data", command=self.save_data).pack(side=tk.LEFT, padx=5)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=5)
        
        # Status label
        self.status_label = ttk.Label(control_frame, text="Ready - Load a video to start")
        self.status_label.pack(side=tk.RIGHT, padx=5)
        
        # Create notebook for tabs
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Video tab
        video_frame = ttk.Frame(notebook)
        notebook.add(video_frame, text="Live Video")
        self.setup_video_tab(video_frame)
        
        # Heatmap tab
        heatmap_frame = ttk.Frame(notebook)
        notebook.add(heatmap_frame, text="Heatmap Analysis")
        self.setup_heatmap_tab(heatmap_frame)
        
        # Bird's eye view tab
        birds_eye_frame = ttk.Frame(notebook)
        notebook.add(birds_eye_frame, text="Bird's Eye View")
        self.setup_birds_eye_tab(birds_eye_frame)
        
        # Statistics tab
        stats_frame = ttk.Frame(notebook)
        notebook.add(stats_frame, text="Statistics")
        self.setup_stats_tab(stats_frame)
        
    def setup_video_tab(self, parent):
        # Video display
        self.video_label = tk.Label(parent, bg='black', text="No video loaded")
        self.video_label.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
    def setup_heatmap_tab(self, parent):
        # Heatmap visualization
        self.heatmap_fig = Figure(figsize=(10, 6), dpi=100)
        self.heatmap_ax = self.heatmap_fig.add_subplot(111)
        self.heatmap_canvas = FigureCanvasTkAgg(self.heatmap_fig, parent)
        self.heatmap_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Heatmap controls
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(controls_frame, text="Heatmap Type:").pack(side=tk.LEFT)
        self.heatmap_type = ttk.Combobox(controls_frame, values=["All Players", "Individual Players"], state="readonly")
        self.heatmap_type.set("All Players")
        self.heatmap_type.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(controls_frame, text="Update Heatmap", command=self.update_heatmap).pack(side=tk.LEFT, padx=5)
        
    def setup_birds_eye_tab(self, parent):
        # Bird's eye view visualization
        self.birds_eye_fig = Figure(figsize=(10, 6), dpi=100)
        self.birds_eye_ax = self.birds_eye_fig.add_subplot(111)
        self.birds_eye_canvas = FigureCanvasTkAgg(self.birds_eye_fig, parent)
        self.birds_eye_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Controls
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(controls_frame, text="Trail Length:").pack(side=tk.LEFT)
        self.trail_length = tk.Scale(controls_frame, from_=5, to=100, orient=tk.HORIZONTAL)
        self.trail_length.set(30)
        self.trail_length.pack(side=tk.LEFT, padx=5)
        
        self.show_trails = tk.BooleanVar(value=True)
        ttk.Checkbutton(controls_frame, text="Show Trails", variable=self.show_trails).pack(side=tk.LEFT, padx=5)
        
    def setup_stats_tab(self, parent):
        # Statistics display
        self.stats_text = tk.Text(parent, wrap=tk.WORD, font=("Courier", 10))
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def load_video(self):
        file_path = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[("Video files", "*.mp4 *.avi *.mov *.mkv"), ("All files", "*.*")]
        )
        
        if file_path:
            if self.cap:
                self.cap.release()
            
            self.cap = cv2.VideoCapture(file_path)
            if not self.cap.isOpened():
                messagebox.showerror("Error", "Could not open video file")
                return
            
            # Get video properties
            self.fps = int(self.cap.get(cv2.CAP_PROP_FPS))
            self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            self.status_label.config(text=f"Video loaded: {self.width}x{self.height}, {self.fps}fps, {total_frames} frames")
            self.reset_analysis()
            
    def toggle_playback(self):
        if not self.cap:
            messagebox.showwarning("Warning", "Please load a video first")
            return
            
        self.is_playing = not self.is_playing
        self.play_button.config(text="Pause" if self.is_playing else "Play")
        
        if self.is_playing:
            self.play_video()
            
    def play_video(self):
        if not self.is_playing or not self.cap:
            return
            
        ret, frame = self.cap.read()
        if not ret:
            self.is_playing = False
            self.play_button.config(text="Play")
            self.status_label.config(text="Video finished")
            return
            
        self.current_frame = frame.copy()
        self.process_frame(frame)
        
        # Schedule next frame
        self.root.after(int(1000/self.fps), self.play_video)
        
    def process_frame(self, frame):
        # Run YOLO detection
        results = self.model(frame)[0]
        detections = []
        
        for data in results.boxes.data.tolist():
            x1, y1, x2, y2, conf, cls = data
            if int(cls) == 0 and conf > 0.4:  # Only person class
                detections.append([int(x1), int(y1), int(x2 - x1), int(y2 - y1), conf, int(cls)])
        
        # Update tracker
        ds_detections = [[d[:4], d[4], d[5]] for d in detections]
        tracks = self.tracker.update_tracks(ds_detections, frame=frame)
        
        # Process tracks
        for track in tracks:
            if not track.is_confirmed():
                continue
                
            track_id = track.track_id
            ltrb = track.to_ltrb()
            x1, y1, x2, y2 = map(int, ltrb)
            center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
            
            # Store tracking data
            position_data = {
                'frame': self.frame_count,
                'x': center_x,
                'y': center_y,
                'bbox': [x1, y1, x2-x1, y2-y1],
                'confidence': track.det_conf if hasattr(track, 'det_conf') else 1.0
            }
            
            self.tracking_data[track_id].append(position_data)
            self.recent_positions[track_id].append((center_x, center_y))
            
            # Update heatmap data
            field_x = int((center_x / self.width) * 100)
            field_y = int((center_y / self.height) * 60)
            field_x = max(0, min(99, field_x))
            field_y = max(0, min(59, field_y))
            self.heatmap_data[track_id][field_y, field_x] += 1
            
            # Draw on frame
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
            cv2.putText(frame, f"ID: {track_id}", (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
        
        # Update video display
        self.update_video_display(frame)
        
        # Update visualizations
        self.update_real_time_visualizations()
        
        self.frame_count += 1
        
        # Update progress
        if self.cap:
            total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            progress = (self.frame_count / total_frames) * 100
            self.progress_var.set(progress)

    def update_video_display(self, frame):
        # Resize frame for display
        display_frame = cv2.resize(frame, (640, 480))
        display_frame = cv2.cvtColor(display_frame, cv2.COLOR_BGR2RGB)

        # Convert to PhotoImage
        from PIL import Image, ImageTk
        image = Image.fromarray(display_frame)
        photo = ImageTk.PhotoImage(image)

        self.video_label.config(image=photo)
        self.video_label.image = photo  # Keep a reference

    def update_real_time_visualizations(self):
        # Update every 10 frames to avoid overwhelming the GUI
        if self.frame_count % 10 == 0:
            self.update_heatmap()
            self.update_birds_eye_view()
            self.update_statistics()

    def update_heatmap(self):
        self.heatmap_ax.clear()

        if self.heatmap_type.get() == "All Players":
            # Combine all player heatmaps
            combined_heatmap = np.zeros((60, 100))
            for track_id, heatmap in self.heatmap_data.items():
                combined_heatmap += heatmap

            if combined_heatmap.max() > 0:
                im = self.heatmap_ax.imshow(combined_heatmap, cmap='hot', interpolation='gaussian',
                                          extent=[0, 100, 0, 60], origin='lower', alpha=0.8)
                self.heatmap_fig.colorbar(im, ax=self.heatmap_ax, label='Activity Intensity')

        self.heatmap_ax.set_title('Player Activity Heatmap')
        self.heatmap_ax.set_xlabel('Field Width (%)')
        self.heatmap_ax.set_ylabel('Field Height (%)')
        self.heatmap_ax.grid(True, alpha=0.3)

        # Draw field boundaries
        self.heatmap_ax.add_patch(plt.Rectangle((0, 0), 100, 60, fill=False, edgecolor='white', linewidth=2))

        self.heatmap_canvas.draw()

    def update_birds_eye_view(self):
        self.birds_eye_ax.clear()

        # Set field dimensions
        field_width, field_height = 100, 60
        self.birds_eye_ax.set_xlim(0, field_width)
        self.birds_eye_ax.set_ylim(0, field_height)

        # Draw field
        self.birds_eye_ax.add_patch(plt.Rectangle((0, 0), field_width, field_height,
                                                 fill=False, edgecolor='green', linewidth=2))

        # Draw center line and circle
        self.birds_eye_ax.axvline(x=field_width/2, color='white', linewidth=1, alpha=0.7)
        circle = plt.Circle((field_width/2, field_height/2), 9.15, fill=False, color='white', alpha=0.7)
        self.birds_eye_ax.add_patch(circle)

        # Plot current positions and trails
        colors = plt.cm.tab20(np.linspace(0, 1, max(len(self.tracking_data), 1)))

        for i, (track_id, positions) in enumerate(self.recent_positions.items()):
            if len(positions) == 0:
                continue

            color = colors[i % len(colors)]

            # Convert positions to field coordinates
            field_positions = [(x / self.width * field_width, y / self.height * field_height)
                             for x, y in positions]

            if self.show_trails.get() and len(field_positions) > 1:
                # Draw trail
                trail_x, trail_y = zip(*field_positions)
                self.birds_eye_ax.plot(trail_x, trail_y, color=color, alpha=0.6, linewidth=2)

            # Draw current position
            if field_positions:
                current_x, current_y = field_positions[-1]
                self.birds_eye_ax.scatter(current_x, current_y, color=color, s=100,
                                        marker='o', edgecolor='white', linewidth=1,
                                        label=f'Player {track_id}')

        self.birds_eye_ax.set_title("Bird's Eye View - Real-time Tracking")
        self.birds_eye_ax.set_xlabel('Field Width (%)')
        self.birds_eye_ax.set_ylabel('Field Height (%)')
        self.birds_eye_ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
        self.birds_eye_ax.set_aspect('equal')

        self.birds_eye_canvas.draw()

    def update_statistics(self):
        stats_text = f"=== REAL-TIME TRACKING STATISTICS ===\n"
        stats_text += f"Frame: {self.frame_count}\n"
        stats_text += f"Active Players: {len(self.recent_positions)}\n"
        stats_text += f"Total Tracked Players: {len(self.tracking_data)}\n\n"

        stats_text += "PLAYER DETAILS:\n"
        stats_text += "-" * 50 + "\n"

        for track_id, positions in self.tracking_data.items():
            if len(positions) > 0:
                latest_pos = positions[-1]
                total_distance = self.calculate_distance_traveled(track_id)
                avg_speed = self.calculate_average_speed(track_id)

                stats_text += f"Player {track_id}:\n"
                stats_text += f"  Position: ({latest_pos['x']}, {latest_pos['y']})\n"
                stats_text += f"  Frames tracked: {len(positions)}\n"
                stats_text += f"  Distance traveled: {total_distance:.1f} pixels\n"
                stats_text += f"  Average speed: {avg_speed:.1f} pixels/frame\n"
                stats_text += f"  Confidence: {latest_pos['confidence']:.2f}\n\n"

        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(1.0, stats_text)

    def calculate_distance_traveled(self, track_id):
        positions = self.tracking_data[track_id]
        if len(positions) < 2:
            return 0.0

        total_distance = 0.0
        for i in range(1, len(positions)):
            prev_pos = positions[i-1]
            curr_pos = positions[i]
            distance = np.sqrt((curr_pos['x'] - prev_pos['x'])**2 + (curr_pos['y'] - prev_pos['y'])**2)
            total_distance += distance

        return total_distance

    def calculate_average_speed(self, track_id):
        positions = self.tracking_data[track_id]
        if len(positions) < 2:
            return 0.0

        total_distance = self.calculate_distance_traveled(track_id)
        time_frames = len(positions) - 1

        return total_distance / time_frames if time_frames > 0 else 0.0

    def reset_analysis(self):
        self.tracking_data.clear()
        self.heatmap_data.clear()
        self.recent_positions.clear()
        self.frame_count = 0
        self.progress_var.set(0)

        # Reset tracker
        self.tracker = DeepSort(max_age=60, n_init=3, nms_max_overlap=1.0, embedder="mobilenet", half=True)

        # Clear visualizations
        if hasattr(self, 'heatmap_ax'):
            self.heatmap_ax.clear()
            self.heatmap_canvas.draw()

        if hasattr(self, 'birds_eye_ax'):
            self.birds_eye_ax.clear()
            self.birds_eye_canvas.draw()

        if hasattr(self, 'stats_text'):
            self.stats_text.delete(1.0, tk.END)

        self.status_label.config(text="Analysis reset")

    def save_data(self):
        if not self.tracking_data:
            messagebox.showwarning("Warning", "No tracking data to save")
            return

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Save tracking data
        tracking_filename = f"tracking_data_{timestamp}.json"
        with open(tracking_filename, 'w') as f:
            # Convert defaultdict to regular dict for JSON serialization
            json_data = {str(k): v for k, v in self.tracking_data.items()}
            json.dump(json_data, f, indent=2)

        # Save heatmap data
        heatmap_filename = f"heatmap_data_{timestamp}.json"
        with open(heatmap_filename, 'w') as f:
            # Convert numpy arrays to lists for JSON serialization
            heatmap_json = {str(k): v.tolist() for k, v in self.heatmap_data.items()}
            json.dump(heatmap_json, f, indent=2)

        # Save current visualizations
        if hasattr(self, 'heatmap_fig'):
            self.heatmap_fig.savefig(f"heatmap_{timestamp}.png", dpi=300, bbox_inches='tight')

        if hasattr(self, 'birds_eye_fig'):
            self.birds_eye_fig.savefig(f"birds_eye_view_{timestamp}.png", dpi=300, bbox_inches='tight')

        messagebox.showinfo("Success", f"Data saved with timestamp {timestamp}")
        self.status_label.config(text=f"Data saved: {timestamp}")

def main():
    root = tk.Tk()
    app = RealTimeAnalyzerGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
