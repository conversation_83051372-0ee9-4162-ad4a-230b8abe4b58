#!/usr/bin/env python3
"""
Low Performance YOLO Tracking System Launcher
Optimized for systems with limited resources
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def check_requirements():
    """Check if basic requirements are available"""
    required_packages = {
        'cv2': 'opencv-python',
        'numpy': 'numpy', 
        'ultralytics': 'ultralytics',
        'deep_sort_realtime': 'deep-sort-realtime'
    }
    
    missing = []
    for module, package in required_packages.items():
        try:
            __import__(module)
            print(f"✓ {package}")
        except ImportError:
            print(f"✗ {package}")
            missing.append(package)
    
    return missing

def install_missing_packages(packages):
    """Install missing packages"""
    if not packages:
        return True
        
    print(f"\nInstalling missing packages: {', '.join(packages)}")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install"] + packages)
        print("✓ Packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install packages: {e}")
        return False

def setup_low_performance_environment():
    """Set up environment variables for low performance mode"""
    os.environ["YOLO_ENV"] = "low_performance"
    os.environ["YOLO_DEVICE"] = "cpu"
    os.environ["YOLO_CONFIDENCE"] = "0.6"
    
    # Reduce OpenCV thread count
    cv2_threads = "1"
    os.environ["OPENCV_NUM_THREADS"] = cv2_threads
    
    # Reduce numpy thread count
    os.environ["OMP_NUM_THREADS"] = "1"
    os.environ["MKL_NUM_THREADS"] = "1"
    os.environ["NUMEXPR_NUM_THREADS"] = "1"
    
    print("✓ Low performance environment configured")

def check_model_file():
    """Check if YOLO model file exists"""
    model_path = Path("yolo11n.pt")
    if model_path.exists():
        print(f"✓ YOLO model found: {model_path}")
        return True
    else:
        print("✗ YOLO model not found")
        print("The model will be downloaded automatically on first run")
        return True  # YOLO will download it automatically

def main():
    print("🔋 Starting Low Performance YOLO Tracking System")
    print("=" * 50)
    
    # Check requirements
    print("\n📦 Checking requirements...")
    missing = check_requirements()
    
    if missing:
        print(f"\n⚠️  Missing packages detected: {', '.join(missing)}")
        install = input("Install missing packages? (y/n): ").lower().strip()
        if install == 'y':
            if not install_missing_packages(missing):
                print("Failed to install packages. Please install manually:")
                print(f"pip install {' '.join(missing)}")
                return
        else:
            print("Cannot proceed without required packages.")
            return
    
    # Check model
    print("\n🤖 Checking YOLO model...")
    check_model_file()
    
    # Setup environment
    print("\n⚙️  Setting up low performance environment...")
    setup_low_performance_environment()
    
    # Choose interface
    print("\n🖥️  Choose interface:")
    print("1. Low Performance GUI (Recommended)")
    print("2. Simple GUI with low settings")
    print("3. Command line processing")
    
    choice = input("Enter choice (1-3): ").strip()
    
    if choice == "1":
        print("\n🚀 Starting Low Performance GUI...")
        try:
            from low_performance_gui import main as gui_main
            gui_main()
        except ImportError as e:
            print(f"✗ Failed to import low performance GUI: {e}")
            print("Falling back to simple GUI...")
            choice = "2"
    
    if choice == "2":
        print("\n🚀 Starting Simple GUI with low performance settings...")
        try:
            # Set low performance config before importing
            os.environ["YOLO_ENV"] = "testing"  # Use testing config which has low settings
            from simple_gui import main as simple_main
            simple_main()
        except Exception as e:
            print(f"✗ Failed to start simple GUI: {e}")
            choice = "3"
    
    if choice == "3":
        print("\n🚀 Starting command line processing...")
        print("This will process the video in input_video/test.mp4")
        try:
            # Import and run basic processing
            import yolo_interface
        except Exception as e:
            print(f"✗ Failed to start command line processing: {e}")
    
    print("\n✅ Low Performance Mode Setup Complete")
    print("\nTips for better performance:")
    print("- Close other applications to free up RAM")
    print("- Use smaller video files when possible")
    print("- Consider reducing video resolution before processing")
    print("- The system processes every 3rd frame to reduce load")

if __name__ == "__main__":
    main()
