#!/usr/bin/env python3
"""
Performance Optimization Script
Automatically configures the system for optimal performance on low-end hardware
"""

import os
import sys
import json
import subprocess
from pathlib import Path

def set_environment_variables():
    """Set environment variables for optimal performance"""
    print("🔧 Setting performance environment variables...")
    
    # YOLO optimizations
    os.environ["YOLO_ENV"] = "low_performance"
    os.environ["YOLO_DEVICE"] = "cpu"
    os.environ["YOLO_CONFIDENCE"] = "0.6"
    
    # OpenCV optimizations
    os.environ["OPENCV_NUM_THREADS"] = "1"
    os.environ["OPENCV_DNN_BACKEND"] = "0"  # Default backend
    
    # NumPy/BLAS optimizations
    os.environ["OMP_NUM_THREADS"] = "1"
    os.environ["MKL_NUM_THREADS"] = "1"
    os.environ["NUMEXPR_NUM_THREADS"] = "1"
    os.environ["OPENBLAS_NUM_THREADS"] = "1"
    
    # PyTorch optimizations
    os.environ["TORCH_NUM_THREADS"] = "1"
    
    print("✓ Environment variables set for low performance mode")

def create_optimized_config():
    """Create an optimized configuration file"""
    print("📝 Creating optimized configuration...")
    
    config_path = Path("config_optimized.json")
    
    optimized_config = {
        "yolo": {
            "model_path": "yolo11n.pt",
            "confidence_threshold": 0.7,  # Higher threshold = fewer detections
            "nms_threshold": 0.8,
            "max_detections": 10,          # Limit detections
            "device": "cpu",               # Force CPU
            "half_precision": False        # Disable for CPU
        },
        "tracker": {
            "max_age": 10,                 # Shorter tracking
            "n_init": 2,                   # Faster initialization
            "nms_max_overlap": 0.9,
            "embedder": "mobilenet",
            "half": False,
            "max_cosine_distance": 0.5,
            "nn_budget": 20                # Reduced budget
        },
        "processing": {
            "max_frame_width": 320,        # Very low resolution
            "max_frame_height": 240,
            "target_fps": 10,              # Low FPS
            "frame_skip": 5,               # Process every 5th frame
            "batch_size": 1,
            "async_processing": False,
            "max_queue_size": 3
        },
        "gui": {
            "window_width": 800,
            "window_height": 600,
            "update_interval": 3000,       # Update every 3 seconds
            "max_trail_length": 10,
            "default_trail_length": 5,
            "heatmap_resolution": [20, 30], # Very low resolution
            "auto_save_interval": 300
        }
    }
    
    with open(config_path, 'w') as f:
        json.dump(optimized_config, f, indent=2)
    
    print(f"✓ Optimized config saved to {config_path}")

def check_system_resources():
    """Check available system resources"""
    print("💻 Checking system resources...")
    
    try:
        import psutil
        
        # Memory info
        memory = psutil.virtual_memory()
        print(f"RAM: {memory.total // (1024**3)}GB total, {memory.available // (1024**3)}GB available")
        
        # CPU info
        cpu_count = psutil.cpu_count()
        print(f"CPU: {cpu_count} cores")
        
        # Recommendations
        if memory.available < 2 * (1024**3):  # Less than 2GB available
            print("⚠️  Low memory detected. Consider closing other applications.")
        
        if cpu_count < 4:
            print("⚠️  Limited CPU cores. Performance may be reduced.")
            
    except ImportError:
        print("psutil not available. Install with: pip install psutil")

def optimize_opencv():
    """Apply OpenCV optimizations"""
    print("🎥 Applying OpenCV optimizations...")
    
    try:
        import cv2
        
        # Set number of threads
        cv2.setNumThreads(1)
        
        # Disable OpenCL if available
        if cv2.ocl.haveOpenCL():
            cv2.ocl.setUseOpenCL(False)
            print("✓ OpenCL disabled for better CPU performance")
        
        print("✓ OpenCV optimized for single-threaded performance")
        
    except ImportError:
        print("OpenCV not available")

def create_launch_script():
    """Create an optimized launch script"""
    print("🚀 Creating optimized launch script...")
    
    script_content = '''#!/usr/bin/env python3
"""
Optimized YOLO Tracker Launcher
"""

import os
import sys

# Set performance environment variables
os.environ["YOLO_ENV"] = "low_performance"
os.environ["OPENCV_NUM_THREADS"] = "1"
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["MKL_NUM_THREADS"] = "1"

def main():
    print("🔋 Starting Optimized YOLO Tracker...")
    
    # Try low performance GUI first
    try:
        from low_performance_gui import main as low_perf_main
        low_perf_main()
    except ImportError:
        print("Low performance GUI not available, trying simple GUI...")
        try:
            from simple_gui import main as simple_main
            simple_main()
        except ImportError:
            print("GUI not available, running command line version...")
            import yolo_interface

if __name__ == "__main__":
    main()
'''
    
    with open("run_optimized.py", 'w') as f:
        f.write(script_content)
    
    # Make executable on Unix systems
    if os.name != 'nt':
        os.chmod("run_optimized.py", 0o755)
    
    print("✓ Optimized launcher created: run_optimized.py")

def main():
    print("🔋 YOLO Tracker Performance Optimizer")
    print("=" * 40)
    
    # Check system resources
    check_system_resources()
    
    # Set environment variables
    set_environment_variables()
    
    # Create optimized config
    create_optimized_config()
    
    # Optimize OpenCV
    optimize_opencv()
    
    # Create launch script
    create_launch_script()
    
    print("\n✅ Performance optimization complete!")
    print("\nTo run the optimized system:")
    print("1. python run_optimized.py        (Recommended)")
    print("2. python run_low_performance.py  (Alternative)")
    print("3. python low_performance_gui.py  (Direct GUI)")
    
    print("\nOptimizations applied:")
    print("- CPU-only processing (no GPU memory usage)")
    print("- Reduced model confidence threshold (fewer false positives)")
    print("- Limited detections per frame (max 10-15)")
    print("- Reduced tracking parameters")
    print("- Lower resolution processing")
    print("- Frame skipping (process every 3-5 frames)")
    print("- Reduced GUI update frequency")
    print("- Single-threaded processing")

if __name__ == "__main__":
    main()
