"""
Configuration Management for Enhanced YOLO Tracking System
Provides centralized configuration with environment-specific settings
"""

import os
import json
import logging
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional
from pathlib import Path

@dataclass
class RedisConfig:
    """Redis configuration settings"""
    host: str = "localhost"
    port: int = 6379
    db: int = 0
    password: Optional[str] = None
    socket_timeout: int = 30
    socket_connect_timeout: int = 30
    retry_on_timeout: bool = True
    health_check_interval: int = 30

@dataclass
class CeleryConfig:
    """Celery configuration settings"""
    broker_url: str = "redis://localhost:6379/0"
    result_backend: str = "redis://localhost:6379/0"
    task_serializer: str = "json"
    result_serializer: str = "json"
    accept_content: list = None
    timezone: str = "UTC"
    enable_utc: bool = True
    worker_prefetch_multiplier: int = 1
    task_acks_late: bool = True
    worker_max_tasks_per_child: int = 1000
    task_reject_on_worker_lost: bool = True
    result_expires: int = 3600
    
    def __post_init__(self):
        if self.accept_content is None:
            self.accept_content = ["json"]

@dataclass
class YOLOConfig:
    """YOLO model configuration"""
    model_path: str = "yolo11n.pt"
    confidence_threshold: float = 0.4
    nms_threshold: float = 0.5
    max_detections: int = 100
    device: str = "auto"  # auto, cpu, cuda:0, etc.
    half_precision: bool = True
    
@dataclass
class TrackerConfig:
    """DeepSort tracker configuration"""
    max_age: int = 60
    n_init: int = 3
    nms_max_overlap: float = 1.0
    embedder: str = "mobilenet"
    half: bool = True
    max_cosine_distance: float = 0.2
    nn_budget: int = 100

@dataclass
class ProcessingConfig:
    """Video processing configuration"""
    max_frame_width: int = 1920
    max_frame_height: int = 1080
    target_fps: int = 30
    quality_levels: Dict[str, float] = None
    batch_size: int = 1
    async_processing: bool = True
    max_queue_size: int = 100
    
    def __post_init__(self):
        if self.quality_levels is None:
            self.quality_levels = {
                "Low": 0.5,
                "Medium": 0.75,
                "High": 1.0
            }

@dataclass
class PossessionConfig:
    """Possession analysis configuration"""
    field_width: int = 100
    field_height: int = 60
    team_zone_threshold: float = 0.4  # 40% of field width
    possession_change_threshold: int = 5  # frames
    history_length: int = 300  # 5 minutes at 1fps
    update_interval: float = 1.0  # seconds

@dataclass
class GUIConfig:
    """GUI configuration"""
    window_width: int = 1600
    window_height: int = 1000
    update_interval: int = 500  # milliseconds
    max_trail_length: int = 100
    default_trail_length: int = 30
    heatmap_resolution: tuple = (60, 100)
    auto_save_interval: int = 300  # seconds

@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_path: Optional[str] = None
    max_file_size: int = 10 * 1024 * 1024  # 10MB
    backup_count: int = 5
    console_output: bool = True

@dataclass
class SystemConfig:
    """Main system configuration"""
    redis: RedisConfig
    celery: CeleryConfig
    yolo: YOLOConfig
    tracker: TrackerConfig
    processing: ProcessingConfig
    possession: PossessionConfig
    gui: GUIConfig
    logging: LoggingConfig
    
    # System-wide settings
    environment: str = "development"
    debug: bool = False
    data_directory: str = "./data"
    temp_directory: str = "./temp"
    max_concurrent_videos: int = 5
    cleanup_interval: int = 300  # seconds

class ConfigManager:
    """Configuration manager with environment support"""
    
    def __init__(self, config_file: Optional[str] = None, environment: Optional[str] = None):
        self.config_file = config_file or "config.json"
        self.environment = environment or os.getenv("YOLO_ENV", "development")
        self.config = self._load_config()
        self._setup_logging()
        
    def _load_config(self) -> SystemConfig:
        """Load configuration from file and environment variables"""
        # Default configuration
        config_dict = self._get_default_config()
        
        # Load from file if exists
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r') as f:
                    file_config = json.load(f)
                    
                # Merge environment-specific config
                if self.environment in file_config:
                    config_dict.update(file_config[self.environment])
                elif "default" in file_config:
                    config_dict.update(file_config["default"])
                    
            except Exception as e:
                logging.warning(f"Failed to load config file {self.config_file}: {e}")
        
        # Override with environment variables
        config_dict = self._apply_env_overrides(config_dict)
        
        # Create config objects
        return SystemConfig(
            redis=RedisConfig(**config_dict.get("redis", {})),
            celery=CeleryConfig(**config_dict.get("celery", {})),
            yolo=YOLOConfig(**config_dict.get("yolo", {})),
            tracker=TrackerConfig(**config_dict.get("tracker", {})),
            processing=ProcessingConfig(**config_dict.get("processing", {})),
            possession=PossessionConfig(**config_dict.get("possession", {})),
            gui=GUIConfig(**config_dict.get("gui", {})),
            logging=LoggingConfig(**config_dict.get("logging", {})),
            environment=config_dict.get("environment", "development"),
            debug=config_dict.get("debug", False),
            data_directory=config_dict.get("data_directory", "./data"),
            temp_directory=config_dict.get("temp_directory", "./temp"),
            max_concurrent_videos=config_dict.get("max_concurrent_videos", 5),
            cleanup_interval=config_dict.get("cleanup_interval", 300)
        )
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration dictionary"""
        return {
            "redis": {},
            "celery": {},
            "yolo": {},
            "tracker": {},
            "processing": {},
            "possession": {},
            "gui": {},
            "logging": {},
            "environment": "development",
            "debug": False
        }
    
    def _apply_env_overrides(self, config_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Apply environment variable overrides"""
        env_mappings = {
            "REDIS_HOST": ("redis", "host"),
            "REDIS_PORT": ("redis", "port"),
            "REDIS_PASSWORD": ("redis", "password"),
            "REDIS_DB": ("redis", "db"),
            "CELERY_BROKER_URL": ("celery", "broker_url"),
            "CELERY_RESULT_BACKEND": ("celery", "result_backend"),
            "YOLO_MODEL_PATH": ("yolo", "model_path"),
            "YOLO_CONFIDENCE": ("yolo", "confidence_threshold"),
            "YOLO_DEVICE": ("yolo", "device"),
            "DEBUG": ("debug",),
            "DATA_DIRECTORY": ("data_directory",),
            "TEMP_DIRECTORY": ("temp_directory",)
        }
        
        for env_var, config_path in env_mappings.items():
            value = os.getenv(env_var)
            if value is not None:
                # Navigate to the correct nested dictionary
                current = config_dict
                for key in config_path[:-1]:
                    if key not in current:
                        current[key] = {}
                    current = current[key]
                
                # Convert value to appropriate type
                final_key = config_path[-1]
                if env_var in ["REDIS_PORT", "REDIS_DB"]:
                    current[final_key] = int(value)
                elif env_var in ["YOLO_CONFIDENCE"]:
                    current[final_key] = float(value)
                elif env_var in ["DEBUG"]:
                    current[final_key] = value.lower() in ("true", "1", "yes")
                else:
                    current[final_key] = value
        
        return config_dict
    
    def _setup_logging(self):
        """Setup logging based on configuration"""
        log_config = self.config.logging

        # Create formatters
        formatter = logging.Formatter(log_config.format)

        # Setup root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_config.level.upper()))

        # Clear existing handlers
        root_logger.handlers.clear()

        # Console handler
        if log_config.console_output:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)
            root_logger.addHandler(console_handler)

        # File handler
        if log_config.file_path:
            from logging.handlers import RotatingFileHandler
            import os

            # Ensure log directory exists
            log_dir = os.path.dirname(log_config.file_path)
            if log_dir:
                os.makedirs(log_dir, exist_ok=True)

            file_handler = RotatingFileHandler(
                log_config.file_path,
                maxBytes=log_config.max_file_size,
                backupCount=log_config.backup_count
            )
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
    
    def save_config(self, file_path: Optional[str] = None):
        """Save current configuration to file"""
        file_path = file_path or self.config_file
        
        config_dict = {
            self.environment: {
                "redis": asdict(self.config.redis),
                "celery": asdict(self.config.celery),
                "yolo": asdict(self.config.yolo),
                "tracker": asdict(self.config.tracker),
                "processing": asdict(self.config.processing),
                "possession": asdict(self.config.possession),
                "gui": asdict(self.config.gui),
                "logging": asdict(self.config.logging),
                "environment": self.config.environment,
                "debug": self.config.debug,
                "data_directory": self.config.data_directory,
                "temp_directory": self.config.temp_directory,
                "max_concurrent_videos": self.config.max_concurrent_videos,
                "cleanup_interval": self.config.cleanup_interval
            }
        }
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(file_path) if os.path.dirname(file_path) else ".", exist_ok=True)
        
        with open(file_path, 'w') as f:
            json.dump(config_dict, f, indent=2)
    
    def get_redis_url(self) -> str:
        """Get Redis connection URL"""
        redis_config = self.config.redis
        if redis_config.password:
            return f"redis://:{redis_config.password}@{redis_config.host}:{redis_config.port}/{redis_config.db}"
        else:
            return f"redis://{redis_config.host}:{redis_config.port}/{redis_config.db}"
    
    def ensure_directories(self):
        """Ensure required directories exist"""
        directories = [
            self.config.data_directory,
            self.config.temp_directory
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)

# Global configuration instance
config_manager = ConfigManager()
config = config_manager.config
