"""
Celery Tasks for YOLO Tracking System
Handles frame processing, analytics, and data management
"""

import cv2
import numpy as np
import json
import time
from datetime import datetime, timedelta
from collections import defaultdict, deque
from celery import Task
from celery_config import app, TaskPriority
from ultralytics import YOLO
from deep_sort_realtime.deepsort_tracker import DeepSort
import redis
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Redis client for shared data
redis_client = redis.Redis.from_url(app.conf.broker_url, decode_responses=True)

class CallbackTask(Task):
    """Base task class with callbacks"""
    def on_success(self, retval, task_id, args, kwargs):
        logger.info(f"Task {task_id} succeeded")
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        logger.error(f"Task {task_id} failed: {exc}")

@app.task(base=CallbackTask, bind=True, priority=TaskPriority.HIGH)
def process_frame(self, frame_data, frame_number, video_id):
    """
    Process a single frame for object detection and tracking
    High priority task for real-time processing
    """
    try:
        # Decode frame data
        frame = cv2.imdecode(np.frombuffer(frame_data, np.uint8), cv2.IMREAD_COLOR)
        
        # Get or create YOLO model (cached)
        model_key = f"yolo_model_{video_id}"
        if not hasattr(process_frame, 'models'):
            process_frame.models = {}
        
        if model_key not in process_frame.models:
            # Load model with performance optimizations
            model = YOLO("yolo11n.pt")
            model.to('cpu')  # Force CPU to reduce GPU memory usage
            process_frame.models[model_key] = model
        
        model = process_frame.models[model_key]
        
        # Get or create tracker (cached)
        tracker_key = f"tracker_{video_id}"
        if not hasattr(process_frame, 'trackers'):
            process_frame.trackers = {}
        
        if tracker_key not in process_frame.trackers:
            process_frame.trackers[tracker_key] = DeepSort(
                max_age=60, n_init=3, nms_max_overlap=1.0, 
                embedder="mobilenet", half=True
            )
        
        tracker = process_frame.trackers[tracker_key]
        
        # Run detection with performance optimizations
        results = model(frame, conf=0.6, max_det=20)[0]  # Higher confidence, fewer detections
        detections = []

        for data in results.boxes.data.tolist():
            x1, y1, x2, y2, conf, cls = data
            if int(cls) == 0:  # Person class only
                detections.append([int(x1), int(y1), int(x2 - x1), int(y2 - y1), conf, int(cls)])

        # Limit detections to reduce processing load
        detections = detections[:15]
        
        # Update tracker
        ds_detections = [[d[:4], d[4], d[5]] for d in detections]
        tracks = tracker.update_tracks(ds_detections, frame=frame)
        
        # Process tracks
        tracking_results = []
        for track in tracks:
            if not track.is_confirmed():
                continue
            
            track_id = track.track_id
            ltrb = track.to_ltrb()
            x1, y1, x2, y2 = map(int, ltrb)
            center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
            
            track_data = {
                'track_id': track_id,
                'frame': frame_number,
                'x': center_x,
                'y': center_y,
                'bbox': [x1, y1, x2-x1, y2-y1],
                'confidence': track.det_conf if hasattr(track, 'det_conf') else 1.0,
                'timestamp': time.time()
            }
            
            tracking_results.append(track_data)
            
            # Store in Redis for real-time access
            redis_key = f"tracking:{video_id}:{track_id}:{frame_number}"
            redis_client.setex(redis_key, 300, json.dumps(track_data))  # 5 min expiry
        
        # Trigger possession calculation
        calculate_possession.apply_async(
            args=[video_id, frame_number, tracking_results],
            priority=TaskPriority.HIGH
        )
        
        # Trigger heatmap update (lower priority)
        update_heatmap.apply_async(
            args=[video_id, tracking_results],
            priority=TaskPriority.MEDIUM
        )
        
        return {
            'frame_number': frame_number,
            'tracks_count': len(tracking_results),
            'tracks': tracking_results,
            'processing_time': time.time()
        }
        
    except Exception as e:
        logger.error(f"Frame processing failed: {e}")
        self.retry(countdown=1, max_retries=3)

@app.task(base=CallbackTask, priority=TaskPriority.HIGH)
def calculate_possession(video_id, frame_number, tracking_results):
    """
    Calculate ball possession and team statistics
    High priority for real-time possession tracking
    """
    try:
        # Get field dimensions from Redis
        field_width = float(redis_client.get(f"field:{video_id}:width") or 100)
        field_height = float(redis_client.get(f"field:{video_id}:height") or 60)
        
        # Simple possession calculation based on player positions
        # This is a simplified version - in reality you'd need ball detection
        possession_data = {
            'frame': frame_number,
            'timestamp': time.time(),
            'players': len(tracking_results),
            'team_a_players': 0,
            'team_b_players': 0,
            'possession_team': None,
            'possession_zone': None
        }
        
        if tracking_results:
            # Divide field into team zones (simplified)
            team_a_zone = field_width * 0.4  # Left 40%
            team_b_zone = field_width * 0.6  # Right 60%
            
            team_a_count = 0
            team_b_count = 0
            
            for track in tracking_results:
                field_x = (track['x'] / 1920) * field_width  # Assuming 1920px width
                
                if field_x < team_a_zone:
                    team_a_count += 1
                elif field_x > team_b_zone:
                    team_b_count += 1
            
            possession_data['team_a_players'] = team_a_count
            possession_data['team_b_players'] = team_b_count
            
            # Determine possession (simplified logic)
            if team_a_count > team_b_count:
                possession_data['possession_team'] = 'Team A'
                possession_data['possession_zone'] = 'Left'
            elif team_b_count > team_a_count:
                possession_data['possession_team'] = 'Team B'
                possession_data['possession_zone'] = 'Right'
            else:
                possession_data['possession_team'] = 'Neutral'
                possession_data['possession_zone'] = 'Center'
        
        # Store possession data
        redis_key = f"possession:{video_id}:{frame_number}"
        redis_client.setex(redis_key, 300, json.dumps(possession_data))
        
        # Update live possession stats
        update_live_possession_stats.apply_async(
            args=[video_id, possession_data],
            priority=TaskPriority.HIGH
        )
        
        return possession_data
        
    except Exception as e:
        logger.error(f"Possession calculation failed: {e}")
        return None

@app.task(base=CallbackTask, priority=TaskPriority.HIGH)
def update_live_possession_stats(video_id, possession_data):
    """
    Update live possession statistics for real-time display
    """
    try:
        stats_key = f"live_stats:{video_id}"
        
        # Get current stats
        current_stats = redis_client.get(stats_key)
        if current_stats:
            stats = json.loads(current_stats)
        else:
            stats = {
                'total_frames': 0,
                'team_a_possession_frames': 0,
                'team_b_possession_frames': 0,
                'neutral_frames': 0,
                'team_a_possession_percentage': 0.0,
                'team_b_possession_percentage': 0.0,
                'last_update': time.time(),
                'possession_changes': 0,
                'last_possession_team': None
            }
        
        # Update stats
        stats['total_frames'] += 1
        stats['last_update'] = time.time()
        
        if possession_data['possession_team'] == 'Team A':
            stats['team_a_possession_frames'] += 1
        elif possession_data['possession_team'] == 'Team B':
            stats['team_b_possession_frames'] += 1
        else:
            stats['neutral_frames'] += 1
        
        # Check for possession change
        if (stats['last_possession_team'] and 
            stats['last_possession_team'] != possession_data['possession_team']):
            stats['possession_changes'] += 1
        
        stats['last_possession_team'] = possession_data['possession_team']
        
        # Calculate percentages
        if stats['total_frames'] > 0:
            stats['team_a_possession_percentage'] = (
                stats['team_a_possession_frames'] / stats['total_frames'] * 100
            )
            stats['team_b_possession_percentage'] = (
                stats['team_b_possession_frames'] / stats['total_frames'] * 100
            )
        
        # Store updated stats
        redis_client.setex(stats_key, 3600, json.dumps(stats))  # 1 hour expiry
        
        return stats
        
    except Exception as e:
        logger.error(f"Live stats update failed: {e}")
        return None

@app.task(base=CallbackTask, priority=TaskPriority.MEDIUM)
def update_heatmap(video_id, tracking_results):
    """
    Update heatmap data for visualization
    Medium priority task
    """
    try:
        heatmap_key = f"heatmap:{video_id}"
        
        # Get current heatmap data
        current_heatmap = redis_client.get(heatmap_key)
        if current_heatmap:
            heatmap_data = json.loads(current_heatmap)
        else:
            heatmap_data = {}
        
        # Update heatmap for each track
        for track in tracking_results:
            track_id = str(track['track_id'])
            
            if track_id not in heatmap_data:
                heatmap_data[track_id] = [[0 for _ in range(100)] for _ in range(60)]
            
            # Convert to field coordinates
            field_x = int((track['x'] / 1920) * 100)  # Assuming 1920px width
            field_y = int((track['y'] / 1080) * 60)   # Assuming 1080px height
            
            field_x = max(0, min(99, field_x))
            field_y = max(0, min(59, field_y))
            
            heatmap_data[track_id][field_y][field_x] += 1
        
        # Store updated heatmap
        redis_client.setex(heatmap_key, 3600, json.dumps(heatmap_data))
        
        return len(tracking_results)

    except Exception as e:
        logger.error(f"Heatmap update failed: {e}")
        return 0

@app.task(base=CallbackTask, priority=TaskPriority.MEDIUM)
def generate_statistics(video_id, frame_number):
    """
    Generate comprehensive statistics for the current frame
    """
    try:
        # Get tracking data for recent frames
        stats = {
            'frame': frame_number,
            'timestamp': time.time(),
            'active_players': 0,
            'total_distance': 0.0,
            'average_speed': 0.0,
            'player_details': {}
        }

        # Get recent tracking data
        pattern = f"tracking:{video_id}:*:{frame_number}"
        keys = redis_client.keys(pattern)

        for key in keys:
            track_data = json.loads(redis_client.get(key))
            track_id = track_data['track_id']

            stats['active_players'] += 1
            stats['player_details'][track_id] = {
                'position': (track_data['x'], track_data['y']),
                'confidence': track_data['confidence']
            }

        # Store statistics
        stats_key = f"frame_stats:{video_id}:{frame_number}"
        redis_client.setex(stats_key, 300, json.dumps(stats))

        return stats

    except Exception as e:
        logger.error(f"Statistics generation failed: {e}")
        return None

@app.task(base=CallbackTask, priority=TaskPriority.LOW)
def save_data(video_id, data_type, data):
    """
    Save data to persistent storage
    Low priority background task
    """
    try:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{data_type}_{video_id}_{timestamp}.json"

        with open(filename, 'w') as f:
            json.dump(data, f, indent=2)

        logger.info(f"Data saved to {filename}")
        return filename

    except Exception as e:
        logger.error(f"Data saving failed: {e}")
        return None

@app.task(base=CallbackTask, priority=TaskPriority.LOW)
def cleanup_old_data():
    """
    Periodic task to clean up old Redis data
    """
    try:
        current_time = time.time()
        cutoff_time = current_time - 3600  # 1 hour ago

        # Clean up old tracking data
        pattern = "tracking:*"
        keys = redis_client.keys(pattern)

        deleted_count = 0
        for key in keys:
            try:
                data = json.loads(redis_client.get(key))
                if data.get('timestamp', 0) < cutoff_time:
                    redis_client.delete(key)
                    deleted_count += 1
            except:
                # Delete corrupted data
                redis_client.delete(key)
                deleted_count += 1

        logger.info(f"Cleaned up {deleted_count} old tracking records")
        return deleted_count

    except Exception as e:
        logger.error(f"Cleanup failed: {e}")
        return 0

@app.task(base=CallbackTask, priority=TaskPriority.HIGH)
def update_possession_stats():
    """
    Periodic task to update possession statistics
    """
    try:
        # Get all active video sessions
        pattern = "live_stats:*"
        keys = redis_client.keys(pattern)

        updated_sessions = 0
        for key in keys:
            try:
                stats = json.loads(redis_client.get(key))

                # Check if session is still active (updated within last 30 seconds)
                if time.time() - stats.get('last_update', 0) < 30:
                    updated_sessions += 1

                    # Broadcast updated stats to connected clients
                    broadcast_key = f"broadcast:{key.split(':')[1]}"
                    redis_client.publish(broadcast_key, json.dumps(stats))

            except Exception as e:
                logger.error(f"Failed to update stats for {key}: {e}")

        return updated_sessions

    except Exception as e:
        logger.error(f"Possession stats update failed: {e}")
        return 0

# Task monitoring and health check
@app.task(base=CallbackTask, priority=TaskPriority.LOW)
def health_check():
    """
    Health check task for monitoring system status
    """
    try:
        # Check Redis connection
        redis_client.ping()

        # Check task queue status
        inspect = app.control.inspect()
        active_tasks = inspect.active()

        health_status = {
            'timestamp': time.time(),
            'redis_status': 'healthy',
            'active_tasks': len(active_tasks) if active_tasks else 0,
            'system_status': 'healthy'
        }

        # Store health status
        redis_client.setex('system:health', 60, json.dumps(health_status))

        return health_status

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {'system_status': 'unhealthy', 'error': str(e)}
