import cv2
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import threading
import time
import json
import redis
from collections import defaultdict, deque
from datetime import datetime
from celery_config import app as celery_app
from yolo_tracker.tasks import process_frame, calculate_possession, health_check
import logging
from PIL import Image, ImageTk

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedAnalyzerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Enhanced YOLO Tracking & Possession Analysis")
        self.root.geometry("1600x1000")
        
        # Initialize Redis connection
        try:
            self.redis_client = redis.Redis.from_url(celery_app.conf.broker_url, decode_responses=True)
            self.redis_client.ping()
            logger.info("Redis connection established")
        except Exception as e:
            logger.error(f"Redis connection failed: {e}")
            messagebox.showerror("Error", "Could not connect to Redis. Please ensure <PERSON><PERSON> is running.")
            return
        
        # Video and tracking variables
        self.cap = None
        self.is_playing = False
        self.current_frame = None
        self.frame_count = 0
        self.video_id = None
        self.fps = 30
        self.width = 640
        self.height = 480
        
        # Possession tracking
        self.possession_history = deque(maxlen=300)  # Last 5 minutes at 1fps
        self.live_stats = {}
        
        # GUI update flags
        self.update_possession = True
        self.update_heatmap = True
        self.update_birds_eye = True
        
        self.setup_gui()
        self.start_background_tasks()
        
    def setup_gui(self):
        # Create main frames
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Control buttons
        ttk.Button(control_frame, text="Load Video", command=self.load_video).pack(side=tk.LEFT, padx=5)
        self.play_button = ttk.Button(control_frame, text="Play", command=self.toggle_playback)
        self.play_button.pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Reset", command=self.reset_analysis).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Save Data", command=self.save_data).pack(side=tk.LEFT, padx=5)
        
        # System status
        self.status_frame = ttk.LabelFrame(control_frame, text="System Status")
        self.status_frame.pack(side=tk.RIGHT, padx=5)
        
        self.redis_status = ttk.Label(self.status_frame, text="Redis: Connected", foreground="green")
        self.redis_status.pack(side=tk.LEFT, padx=5)
        
        self.celery_status = ttk.Label(self.status_frame, text="Celery: Active", foreground="green")
        self.celery_status.pack(side=tk.LEFT, padx=5)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, expand=True, padx=5)
        
        # Status label
        self.status_label = ttk.Label(control_frame, text="Ready - Load a video to start")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # Create notebook for tabs
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Video tab
        video_frame = ttk.Frame(notebook)
        notebook.add(video_frame, text="Live Video")
        self.setup_video_tab(video_frame)
        
        # Possession Analytics tab (NEW)
        possession_frame = ttk.Frame(notebook)
        notebook.add(possession_frame, text="Possession Analytics")
        self.setup_possession_tab(possession_frame)
        
        # Heatmap tab
        heatmap_frame = ttk.Frame(notebook)
        notebook.add(heatmap_frame, text="Heatmap Analysis")
        self.setup_heatmap_tab(heatmap_frame)
        
        # Bird's eye view tab
        birds_eye_frame = ttk.Frame(notebook)
        notebook.add(birds_eye_frame, text="Bird's Eye View")
        self.setup_birds_eye_tab(birds_eye_frame)
        
        # Live Statistics tab
        stats_frame = ttk.Frame(notebook)
        notebook.add(stats_frame, text="Live Statistics")
        self.setup_stats_tab(stats_frame)
        
        # System Monitor tab (NEW)
        monitor_frame = ttk.Frame(notebook)
        notebook.add(monitor_frame, text="System Monitor")
        self.setup_monitor_tab(monitor_frame)
        
    def setup_video_tab(self, parent):
        # Video display with enhanced controls
        video_container = ttk.Frame(parent)
        video_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.video_label = tk.Label(video_container, bg='black', text="No video loaded")
        self.video_label.pack(fill=tk.BOTH, expand=True)
        
        # Video controls
        controls = ttk.Frame(video_container)
        controls.pack(fill=tk.X, pady=5)
        
        ttk.Label(controls, text="Processing Quality:").pack(side=tk.LEFT)
        self.quality_var = tk.StringVar(value="High")
        quality_combo = ttk.Combobox(controls, textvariable=self.quality_var, 
                                   values=["Low", "Medium", "High"], state="readonly")
        quality_combo.pack(side=tk.LEFT, padx=5)
        
        ttk.Label(controls, text="Detection Threshold:").pack(side=tk.LEFT, padx=(20,5))
        self.threshold_var = tk.DoubleVar(value=0.4)
        threshold_scale = tk.Scale(controls, variable=self.threshold_var, from_=0.1, to=0.9, 
                                 resolution=0.1, orient=tk.HORIZONTAL)
        threshold_scale.pack(side=tk.LEFT, padx=5)
        
    def setup_possession_tab(self, parent):
        # Main possession analytics display
        main_container = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        main_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Left panel - Live possession stats
        left_panel = ttk.LabelFrame(main_container, text="Live Possession Statistics")
        main_container.add(left_panel, weight=1)
        
        # Possession percentages
        self.possession_frame = ttk.Frame(left_panel)
        self.possession_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # Team A
        team_a_frame = ttk.LabelFrame(self.possession_frame, text="Team A")
        team_a_frame.pack(fill=tk.X, pady=5)
        
        self.team_a_percentage = tk.StringVar(value="0.0%")
        self.team_a_label = ttk.Label(team_a_frame, textvariable=self.team_a_percentage, 
                                     font=("Arial", 24, "bold"), foreground="blue")
        self.team_a_label.pack(pady=10)
        
        self.team_a_progress = ttk.Progressbar(team_a_frame, maximum=100)
        self.team_a_progress.pack(fill=tk.X, padx=10, pady=5)
        
        # Team B
        team_b_frame = ttk.LabelFrame(self.possession_frame, text="Team B")
        team_b_frame.pack(fill=tk.X, pady=5)
        
        self.team_b_percentage = tk.StringVar(value="0.0%")
        self.team_b_label = ttk.Label(team_b_frame, textvariable=self.team_b_percentage, 
                                     font=("Arial", 24, "bold"), foreground="red")
        self.team_b_label.pack(pady=10)
        
        self.team_b_progress = ttk.Progressbar(team_b_frame, maximum=100)
        self.team_b_progress.pack(fill=tk.X, padx=10, pady=5)
        
        # Additional stats
        stats_frame = ttk.LabelFrame(left_panel, text="Match Statistics")
        stats_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.possession_changes_var = tk.StringVar(value="Possession Changes: 0")
        ttk.Label(stats_frame, textvariable=self.possession_changes_var).pack(anchor=tk.W, padx=5, pady=2)
        
        self.current_possession_var = tk.StringVar(value="Current Possession: Neutral")
        ttk.Label(stats_frame, textvariable=self.current_possession_var).pack(anchor=tk.W, padx=5, pady=2)
        
        self.total_players_var = tk.StringVar(value="Active Players: 0")
        ttk.Label(stats_frame, textvariable=self.total_players_var).pack(anchor=tk.W, padx=5, pady=2)
        
        # Right panel - Possession timeline
        right_panel = ttk.LabelFrame(main_container, text="Possession Timeline")
        main_container.add(right_panel, weight=2)
        
        # Possession timeline chart
        self.possession_fig = Figure(figsize=(8, 6), dpi=100)
        self.possession_ax = self.possession_fig.add_subplot(111)
        self.possession_canvas = FigureCanvasTkAgg(self.possession_fig, right_panel)
        self.possession_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Timeline controls
        timeline_controls = ttk.Frame(right_panel)
        timeline_controls.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(timeline_controls, text="Timeline Range:").pack(side=tk.LEFT)
        self.timeline_range = ttk.Combobox(timeline_controls, values=["30s", "1m", "2m", "5m"], state="readonly")
        self.timeline_range.set("1m")
        self.timeline_range.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(timeline_controls, text="Reset Timeline", command=self.reset_possession_timeline).pack(side=tk.LEFT, padx=5)

    def setup_heatmap_tab(self, parent):
        # Enhanced heatmap with team separation
        self.heatmap_fig = Figure(figsize=(12, 8), dpi=100)
        self.heatmap_canvas = FigureCanvasTkAgg(self.heatmap_fig, parent)
        self.heatmap_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Heatmap controls
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(controls_frame, text="View Type:").pack(side=tk.LEFT)
        self.heatmap_type = ttk.Combobox(controls_frame, values=["Combined", "Team A", "Team B", "Individual"], state="readonly")
        self.heatmap_type.set("Combined")
        self.heatmap_type.pack(side=tk.LEFT, padx=5)

        ttk.Label(controls_frame, text="Intensity:").pack(side=tk.LEFT, padx=(20,5))
        self.heatmap_intensity = tk.Scale(controls_frame, from_=0.1, to=2.0, resolution=0.1, orient=tk.HORIZONTAL)
        self.heatmap_intensity.set(1.0)
        self.heatmap_intensity.pack(side=tk.LEFT, padx=5)

        ttk.Button(controls_frame, text="Update", command=self.update_heatmap_display).pack(side=tk.LEFT, padx=5)

    def setup_birds_eye_tab(self, parent):
        # Enhanced bird's eye view with team colors
        self.birds_eye_fig = Figure(figsize=(12, 8), dpi=100)
        self.birds_eye_ax = self.birds_eye_fig.add_subplot(111)
        self.birds_eye_canvas = FigureCanvasTkAgg(self.birds_eye_fig, parent)
        self.birds_eye_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Controls
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(controls_frame, text="Trail Length:").pack(side=tk.LEFT)
        self.trail_length = tk.Scale(controls_frame, from_=5, to=100, orient=tk.HORIZONTAL)
        self.trail_length.set(30)
        self.trail_length.pack(side=tk.LEFT, padx=5)

        self.show_trails = tk.BooleanVar(value=True)
        ttk.Checkbutton(controls_frame, text="Show Trails", variable=self.show_trails).pack(side=tk.LEFT, padx=5)

        self.show_possession_zones = tk.BooleanVar(value=True)
        ttk.Checkbutton(controls_frame, text="Show Possession Zones", variable=self.show_possession_zones).pack(side=tk.LEFT, padx=5)

    def setup_stats_tab(self, parent):
        # Enhanced statistics with real-time updates
        stats_container = ttk.PanedWindow(parent, orient=tk.HORIZONTAL)
        stats_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Left panel - Player statistics
        left_stats = ttk.LabelFrame(stats_container, text="Player Statistics")
        stats_container.add(left_stats, weight=1)

        self.player_stats_tree = ttk.Treeview(left_stats, columns=("ID", "Position", "Distance", "Speed", "Team"), show="headings")
        self.player_stats_tree.heading("ID", text="Player ID")
        self.player_stats_tree.heading("Position", text="Position")
        self.player_stats_tree.heading("Distance", text="Distance")
        self.player_stats_tree.heading("Speed", text="Speed")
        self.player_stats_tree.heading("Team", text="Team")

        stats_scrollbar = ttk.Scrollbar(left_stats, orient=tk.VERTICAL, command=self.player_stats_tree.yview)
        self.player_stats_tree.configure(yscrollcommand=stats_scrollbar.set)

        self.player_stats_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Right panel - System statistics
        right_stats = ttk.LabelFrame(stats_container, text="System Performance")
        stats_container.add(right_stats, weight=1)

        self.system_stats_text = tk.Text(right_stats, wrap=tk.WORD, font=("Courier", 10))
        system_scrollbar = ttk.Scrollbar(right_stats, orient=tk.VERTICAL, command=self.system_stats_text.yview)
        self.system_stats_text.configure(yscrollcommand=system_scrollbar.set)

        self.system_stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        system_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    def setup_monitor_tab(self, parent):
        # System monitoring and task queue status
        monitor_container = ttk.PanedWindow(parent, orient=tk.VERTICAL)
        monitor_container.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Task queue monitor
        queue_frame = ttk.LabelFrame(monitor_container, text="Task Queue Status")
        monitor_container.add(queue_frame, weight=1)

        self.queue_tree = ttk.Treeview(queue_frame, columns=("Queue", "Pending", "Active", "Failed"), show="headings")
        self.queue_tree.heading("Queue", text="Queue")
        self.queue_tree.heading("Pending", text="Pending")
        self.queue_tree.heading("Active", text="Active")
        self.queue_tree.heading("Failed", text="Failed")
        self.queue_tree.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # System resources
        resources_frame = ttk.LabelFrame(monitor_container, text="System Resources")
        monitor_container.add(resources_frame, weight=1)

        self.resources_fig = Figure(figsize=(10, 4), dpi=100)
        self.resources_canvas = FigureCanvasTkAgg(self.resources_fig, resources_frame)
        self.resources_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Control buttons
        monitor_controls = ttk.Frame(resources_frame)
        monitor_controls.pack(fill=tk.X, padx=5, pady=5)

        ttk.Button(monitor_controls, text="Refresh Status", command=self.refresh_system_status).pack(side=tk.LEFT, padx=5)
        ttk.Button(monitor_controls, text="Clear Failed Tasks", command=self.clear_failed_tasks).pack(side=tk.LEFT, padx=5)

    def start_background_tasks(self):
        """Start background threads for real-time updates"""
        # Start possession monitoring thread
        self.possession_thread = threading.Thread(target=self.monitor_possession, daemon=True)
        self.possession_thread.start()

        # Start system monitoring thread
        self.system_thread = threading.Thread(target=self.monitor_system, daemon=True)
        self.system_thread.start()

        # Start GUI update thread
        self.gui_thread = threading.Thread(target=self.update_gui_loop, daemon=True)
        self.gui_thread.start()

    def monitor_possession(self):
        """Background thread to monitor possession statistics"""
        while True:
            try:
                if self.video_id and self.is_playing:
                    # Get live stats from Redis
                    stats_key = f"live_stats:{self.video_id}"
                    stats_data = self.redis_client.get(stats_key)

                    if stats_data:
                        self.live_stats = json.loads(stats_data)

                        # Add to possession history
                        timestamp = time.time()
                        possession_point = {
                            'timestamp': timestamp,
                            'team_a_percentage': self.live_stats.get('team_a_possession_percentage', 0),
                            'team_b_percentage': self.live_stats.get('team_b_possession_percentage', 0),
                            'current_team': self.live_stats.get('last_possession_team', 'Neutral')
                        }
                        self.possession_history.append(possession_point)

                time.sleep(1)  # Update every second

            except Exception as e:
                logger.error(f"Possession monitoring error: {e}")
                time.sleep(5)  # Wait longer on error

    def monitor_system(self):
        """Background thread to monitor system health"""
        while True:
            try:
                # Check Redis connection
                self.redis_client.ping()
                self.root.after(0, lambda: self.redis_status.config(text="Redis: Connected", foreground="green"))

                # Check Celery workers
                inspect = celery_app.control.inspect()
                active_workers = inspect.active()

                if active_workers:
                    self.root.after(0, lambda: self.celery_status.config(text="Celery: Active", foreground="green"))
                else:
                    self.root.after(0, lambda: self.celery_status.config(text="Celery: No Workers", foreground="orange"))

                time.sleep(10)  # Check every 10 seconds

            except Exception as e:
                logger.error(f"System monitoring error: {e}")
                self.root.after(0, lambda: self.redis_status.config(text="Redis: Error", foreground="red"))
                time.sleep(5)

    def update_gui_loop(self):
        """Background thread for GUI updates"""
        while True:
            try:
                if self.video_id:
                    # Schedule GUI updates on main thread
                    self.root.after(0, self.update_possession_display)
                    self.root.after(0, self.update_system_stats)

                time.sleep(0.5)  # Update GUI twice per second

            except Exception as e:
                logger.error(f"GUI update error: {e}")
                time.sleep(1)

    def load_video(self):
        file_path = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[("Video files", "*.mp4 *.avi *.mov *.mkv"), ("All files", "*.*")]
        )

        if file_path:
            if self.cap:
                self.cap.release()

            self.cap = cv2.VideoCapture(file_path)
            if not self.cap.isOpened():
                messagebox.showerror("Error", "Could not open video file")
                return

            # Get video properties
            self.fps = int(self.cap.get(cv2.CAP_PROP_FPS))
            self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))

            # Generate unique video ID
            self.video_id = f"video_{int(time.time())}"

            # Store video properties in Redis
            self.redis_client.setex(f"field:{self.video_id}:width", 3600, self.width)
            self.redis_client.setex(f"field:{self.video_id}:height", 3600, self.height)

            self.status_label.config(text=f"Video loaded: {self.width}x{self.height}, {self.fps}fps, {total_frames} frames")
            self.reset_analysis()

    def toggle_playback(self):
        if not self.cap:
            messagebox.showwarning("Warning", "Please load a video first")
            return

        self.is_playing = not self.is_playing
        self.play_button.config(text="Pause" if self.is_playing else "Play")

        if self.is_playing:
            self.play_video()

    def play_video(self):
        if not self.is_playing or not self.cap:
            return

        ret, frame = self.cap.read()
        if not ret:
            self.is_playing = False
            self.play_button.config(text="Play")
            self.status_label.config(text="Video finished")
            return

        # Process frame using Celery
        self.process_frame_async(frame)

        # Update video display
        self.update_video_display(frame)

        # Update progress
        total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        progress = (self.frame_count / total_frames) * 100
        self.progress_var.set(progress)

        self.frame_count += 1

        # Schedule next frame
        self.root.after(int(1000/self.fps), self.play_video)

    def process_frame_async(self, frame):
        """Process frame using Celery task queue"""
        try:
            # Encode frame for transmission
            _, buffer = cv2.imencode('.jpg', frame)
            frame_data = buffer.tobytes()

            # Submit to high priority queue
            task = process_frame.apply_async(
                args=[frame_data, self.frame_count, self.video_id],
                priority=10  # High priority
            )

            # Store task ID for monitoring
            task_key = f"task:{self.video_id}:{self.frame_count}"
            self.redis_client.setex(task_key, 60, task.id)

        except Exception as e:
            logger.error(f"Frame processing submission failed: {e}")

    def update_video_display(self, frame):
        # Resize frame for display
        display_frame = cv2.resize(frame, (640, 480))
        display_frame = cv2.cvtColor(display_frame, cv2.COLOR_BGR2RGB)

        # Convert to PhotoImage
        image = Image.fromarray(display_frame)
        photo = ImageTk.PhotoImage(image)

        self.video_label.config(image=photo)
        self.video_label.image = photo  # Keep a reference

    def update_possession_display(self):
        """Update possession statistics display"""
        if not self.live_stats:
            return

        try:
            # Update percentage displays
            team_a_pct = self.live_stats.get('team_a_possession_percentage', 0)
            team_b_pct = self.live_stats.get('team_b_possession_percentage', 0)

            self.team_a_percentage.set(f"{team_a_pct:.1f}%")
            self.team_b_percentage.set(f"{team_b_pct:.1f}%")

            self.team_a_progress['value'] = team_a_pct
            self.team_b_progress['value'] = team_b_pct

            # Update additional stats
            changes = self.live_stats.get('possession_changes', 0)
            self.possession_changes_var.set(f"Possession Changes: {changes}")

            current_team = self.live_stats.get('last_possession_team', 'Neutral')
            self.current_possession_var.set(f"Current Possession: {current_team}")

            total_frames = self.live_stats.get('total_frames', 0)
            self.total_players_var.set(f"Total Frames: {total_frames}")

            # Update possession timeline
            self.update_possession_timeline()

        except Exception as e:
            logger.error(f"Possession display update failed: {e}")

    def update_possession_timeline(self):
        """Update the possession timeline chart"""
        if len(self.possession_history) < 2:
            return

        try:
            self.possession_ax.clear()

            # Get timeline range
            range_seconds = {"30s": 30, "1m": 60, "2m": 120, "5m": 300}
            max_age = range_seconds.get(self.timeline_range.get(), 60)

            current_time = time.time()
            recent_data = [p for p in self.possession_history
                          if current_time - p['timestamp'] <= max_age]

            if len(recent_data) < 2:
                return

            timestamps = [p['timestamp'] for p in recent_data]
            team_a_values = [p['team_a_percentage'] for p in recent_data]
            team_b_values = [p['team_b_percentage'] for p in recent_data]

            # Convert timestamps to relative seconds
            base_time = timestamps[0]
            relative_times = [(t - base_time) for t in timestamps]

            self.possession_ax.plot(relative_times, team_a_values, 'b-', label='Team A', linewidth=2)
            self.possession_ax.plot(relative_times, team_b_values, 'r-', label='Team B', linewidth=2)
            self.possession_ax.fill_between(relative_times, team_a_values, alpha=0.3, color='blue')
            self.possession_ax.fill_between(relative_times, team_b_values, alpha=0.3, color='red')

            self.possession_ax.set_xlabel('Time (seconds)')
            self.possession_ax.set_ylabel('Possession %')
            self.possession_ax.set_title('Live Possession Timeline')
            self.possession_ax.legend()
            self.possession_ax.grid(True, alpha=0.3)
            self.possession_ax.set_ylim(0, 100)

            self.possession_canvas.draw()

        except Exception as e:
            logger.error(f"Timeline update failed: {e}")

    def update_heatmap_display(self):
        """Update heatmap visualization from Redis data"""
        if not self.video_id:
            return

        try:
            heatmap_key = f"heatmap:{self.video_id}"
            heatmap_data = self.redis_client.get(heatmap_key)

            if not heatmap_data:
                return

            heatmap_dict = json.loads(heatmap_data)

            self.heatmap_fig.clear()
            ax = self.heatmap_fig.add_subplot(111)

            if self.heatmap_type.get() == "Combined":
                # Combine all player heatmaps
                combined = np.zeros((60, 100))
                for track_id, data in heatmap_dict.items():
                    combined += np.array(data)

                if combined.max() > 0:
                    intensity = self.heatmap_intensity.get()
                    im = ax.imshow(combined * intensity, cmap='hot', interpolation='gaussian',
                                 extent=[0, 100, 0, 60], origin='lower', alpha=0.8)
                    self.heatmap_fig.colorbar(im, ax=ax, label='Activity Intensity')

            ax.set_title(f'Player Activity Heatmap - {self.heatmap_type.get()}')
            ax.set_xlabel('Field Width (%)')
            ax.set_ylabel('Field Height (%)')
            ax.grid(True, alpha=0.3)

            self.heatmap_canvas.draw()

        except Exception as e:
            logger.error(f"Heatmap update failed: {e}")

    def update_system_stats(self):
        """Update system performance statistics"""
        try:
            # Get system health
            health_data = self.redis_client.get('system:health')
            if health_data:
                health = json.loads(health_data)

                stats_text = "=== SYSTEM PERFORMANCE ===\n"
                stats_text += f"System Status: {health.get('system_status', 'Unknown')}\n"
                stats_text += f"Active Tasks: {health.get('active_tasks', 0)}\n"
                stats_text += f"Last Update: {datetime.fromtimestamp(health.get('timestamp', 0)).strftime('%H:%M:%S')}\n\n"

                # Add Redis stats
                info = self.redis_client.info()
                stats_text += "=== REDIS STATISTICS ===\n"
                stats_text += f"Connected Clients: {info.get('connected_clients', 0)}\n"
                stats_text += f"Used Memory: {info.get('used_memory_human', 'Unknown')}\n"
                stats_text += f"Total Commands: {info.get('total_commands_processed', 0)}\n\n"

                # Add task queue stats
                if self.video_id:
                    stats_text += "=== PROCESSING STATISTICS ===\n"
                    stats_text += f"Video ID: {self.video_id}\n"
                    stats_text += f"Frames Processed: {self.frame_count}\n"
                    stats_text += f"Processing Rate: {self.fps} fps\n"

                self.system_stats_text.delete(1.0, tk.END)
                self.system_stats_text.insert(1.0, stats_text)

        except Exception as e:
            logger.error(f"System stats update failed: {e}")

    def refresh_system_status(self):
        """Manually refresh system status"""
        try:
            # Trigger health check
            health_check.apply_async(priority=5)

            # Update queue status
            inspect = celery_app.control.inspect()

            # Clear existing items
            for item in self.queue_tree.get_children():
                self.queue_tree.delete(item)

            # Add queue information
            queues = ["high_priority", "medium_priority", "low_priority"]
            for queue in queues:
                # This is simplified - in production you'd get real queue stats
                self.queue_tree.insert("", "end", values=(queue, "0", "0", "0"))

            self.status_label.config(text="System status refreshed")

        except Exception as e:
            logger.error(f"Status refresh failed: {e}")
            messagebox.showerror("Error", f"Failed to refresh status: {e}")

    def clear_failed_tasks(self):
        """Clear failed tasks from the queue"""
        try:
            # This would typically involve Celery's purge functionality
            celery_app.control.purge()
            self.status_label.config(text="Failed tasks cleared")

        except Exception as e:
            logger.error(f"Failed to clear tasks: {e}")
            messagebox.showerror("Error", f"Failed to clear tasks: {e}")

    def reset_possession_timeline(self):
        """Reset the possession timeline"""
        self.possession_history.clear()
        self.possession_ax.clear()
        self.possession_canvas.draw()

    def reset_analysis(self):
        """Reset all analysis data"""
        if self.video_id:
            # Clear Redis data for this video
            pattern = f"*:{self.video_id}:*"
            keys = self.redis_client.keys(pattern)
            if keys:
                self.redis_client.delete(*keys)

        self.frame_count = 0
        self.progress_var.set(0)
        self.possession_history.clear()
        self.live_stats.clear()

        # Clear displays
        self.team_a_percentage.set("0.0%")
        self.team_b_percentage.set("0.0%")
        self.team_a_progress['value'] = 0
        self.team_b_progress['value'] = 0

        self.status_label.config(text="Analysis reset")

    def save_data(self):
        """Save all analysis data"""
        if not self.video_id:
            messagebox.showwarning("Warning", "No video loaded")
            return

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Save possession data
            possession_file = f"possession_data_{self.video_id}_{timestamp}.json"
            with open(possession_file, 'w') as f:
                json.dump(list(self.possession_history), f, indent=2)

            # Save live stats
            if self.live_stats:
                stats_file = f"live_stats_{self.video_id}_{timestamp}.json"
                with open(stats_file, 'w') as f:
                    json.dump(self.live_stats, f, indent=2)

            # Save visualizations
            if hasattr(self, 'possession_fig'):
                self.possession_fig.savefig(f"possession_timeline_{timestamp}.png", dpi=300, bbox_inches='tight')

            if hasattr(self, 'heatmap_fig'):
                self.heatmap_fig.savefig(f"heatmap_{timestamp}.png", dpi=300, bbox_inches='tight')

            messagebox.showinfo("Success", f"Data saved with timestamp {timestamp}")
            self.status_label.config(text=f"Data saved: {timestamp}")

        except Exception as e:
            logger.error(f"Data saving failed: {e}")
            messagebox.showerror("Error", f"Failed to save data: {e}")

def main():
    root = tk.Tk()
    app = EnhancedAnalyzerGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
