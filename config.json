{"development": {"redis": {"host": "localhost", "port": 6379, "db": 0, "password": null, "socket_timeout": 30, "socket_connect_timeout": 30, "retry_on_timeout": true, "health_check_interval": 30}, "celery": {"broker_url": "redis://localhost:6379/0", "result_backend": "redis://localhost:6379/0", "task_serializer": "json", "result_serializer": "json", "accept_content": ["json"], "timezone": "UTC", "enable_utc": true, "worker_prefetch_multiplier": 1, "task_acks_late": true, "worker_max_tasks_per_child": 1000, "task_reject_on_worker_lost": true, "result_expires": 3600}, "yolo": {"model_path": "yolo11n.pt", "confidence_threshold": 0.4, "nms_threshold": 0.5, "max_detections": 100, "device": "auto", "half_precision": true}, "tracker": {"max_age": 60, "n_init": 3, "nms_max_overlap": 1.0, "embedder": "mobilenet", "half": true, "max_cosine_distance": 0.2, "nn_budget": 100}, "processing": {"max_frame_width": 1920, "max_frame_height": 1080, "target_fps": 30, "quality_levels": {"Low": 0.5, "Medium": 0.75, "High": 1.0}, "batch_size": 1, "async_processing": true, "max_queue_size": 100}, "possession": {"field_width": 100, "field_height": 60, "team_zone_threshold": 0.4, "possession_change_threshold": 5, "history_length": 300, "update_interval": 1.0}, "gui": {"window_width": 1600, "window_height": 1000, "update_interval": 500, "max_trail_length": 100, "default_trail_length": 30, "heatmap_resolution": [60, 100], "auto_save_interval": 300}, "logging": {"level": "DEBUG", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_path": "logs/yolo_tracker_dev.log", "max_file_size": 10485760, "backup_count": 5, "console_output": true}, "environment": "development", "debug": true, "data_directory": "./data", "temp_directory": "./temp", "max_concurrent_videos": 3, "cleanup_interval": 300}, "production": {"redis": {"host": "redis", "port": 6379, "db": 0, "password": null, "socket_timeout": 30, "socket_connect_timeout": 30, "retry_on_timeout": true, "health_check_interval": 60}, "celery": {"broker_url": "redis://redis:6379/0", "result_backend": "redis://redis:6379/0", "task_serializer": "json", "result_serializer": "json", "accept_content": ["json"], "timezone": "UTC", "enable_utc": true, "worker_prefetch_multiplier": 2, "task_acks_late": true, "worker_max_tasks_per_child": 500, "task_reject_on_worker_lost": true, "result_expires": 7200}, "yolo": {"model_path": "yolo11n.pt", "confidence_threshold": 0.5, "nms_threshold": 0.4, "max_detections": 50, "device": "auto", "half_precision": true}, "tracker": {"max_age": 30, "n_init": 3, "nms_max_overlap": 0.8, "embedder": "mobilenet", "half": true, "max_cosine_distance": 0.3, "nn_budget": 50}, "processing": {"max_frame_width": 1280, "max_frame_height": 720, "target_fps": 25, "quality_levels": {"Low": 0.5, "Medium": 0.75, "High": 1.0}, "batch_size": 2, "async_processing": true, "max_queue_size": 200}, "possession": {"field_width": 100, "field_height": 60, "team_zone_threshold": 0.35, "possession_change_threshold": 3, "history_length": 600, "update_interval": 0.5}, "gui": {"window_width": 1400, "window_height": 900, "update_interval": 1000, "max_trail_length": 50, "default_trail_length": 20, "heatmap_resolution": [60, 100], "auto_save_interval": 600}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_path": "/app/logs/yolo_tracker_prod.log", "max_file_size": 52428800, "backup_count": 10, "console_output": false}, "environment": "production", "debug": false, "data_directory": "/app/data", "temp_directory": "/app/temp", "max_concurrent_videos": 10, "cleanup_interval": 180}, "testing": {"redis": {"host": "localhost", "port": 6379, "db": 1, "password": null, "socket_timeout": 10, "socket_connect_timeout": 10, "retry_on_timeout": false, "health_check_interval": 10}, "celery": {"broker_url": "redis://localhost:6379/1", "result_backend": "redis://localhost:6379/1", "task_serializer": "json", "result_serializer": "json", "accept_content": ["json"], "timezone": "UTC", "enable_utc": true, "worker_prefetch_multiplier": 1, "task_acks_late": false, "worker_max_tasks_per_child": 100, "task_reject_on_worker_lost": false, "result_expires": 300}, "yolo": {"model_path": "yolo11n.pt", "confidence_threshold": 0.3, "nms_threshold": 0.6, "max_detections": 20, "device": "cpu", "half_precision": false}, "tracker": {"max_age": 10, "n_init": 2, "nms_max_overlap": 1.0, "embedder": "mobilenet", "half": false, "max_cosine_distance": 0.5, "nn_budget": 20}, "processing": {"max_frame_width": 640, "max_frame_height": 480, "target_fps": 10, "quality_levels": {"Low": 0.5, "Medium": 0.75, "High": 1.0}, "batch_size": 1, "async_processing": false, "max_queue_size": 10}, "possession": {"field_width": 50, "field_height": 30, "team_zone_threshold": 0.5, "possession_change_threshold": 2, "history_length": 60, "update_interval": 2.0}, "gui": {"window_width": 800, "window_height": 600, "update_interval": 2000, "max_trail_length": 20, "default_trail_length": 10, "heatmap_resolution": [30, 50], "auto_save_interval": 60}, "logging": {"level": "DEBUG", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_path": null, "max_file_size": 1048576, "backup_count": 2, "console_output": true}, "environment": "testing", "debug": true, "data_directory": "./test_data", "temp_directory": "./test_temp", "max_concurrent_videos": 1, "cleanup_interval": 60}, "low_performance": {"redis": {"host": "localhost", "port": 6379, "db": 0, "password": null, "socket_timeout": 10, "socket_connect_timeout": 10, "retry_on_timeout": false, "health_check_interval": 60}, "celery": {"broker_url": "redis://localhost:6379/0", "result_backend": "redis://localhost:6379/0", "task_serializer": "json", "result_serializer": "json", "accept_content": ["json"], "timezone": "UTC", "enable_utc": true, "worker_prefetch_multiplier": 1, "task_acks_late": false, "worker_max_tasks_per_child": 100, "task_reject_on_worker_lost": false, "result_expires": 300}, "yolo": {"model_path": "yolo11n.pt", "confidence_threshold": 0.6, "nms_threshold": 0.7, "max_detections": 20, "device": "cpu", "half_precision": false}, "tracker": {"max_age": 15, "n_init": 2, "nms_max_overlap": 0.9, "embedder": "mobilenet", "half": false, "max_cosine_distance": 0.4, "nn_budget": 30}, "processing": {"max_frame_width": 480, "max_frame_height": 360, "target_fps": 10, "quality_levels": {"Low": 0.3, "Medium": 0.5, "High": 0.7}, "batch_size": 1, "async_processing": false, "max_queue_size": 5}, "possession": {"field_width": 50, "field_height": 30, "team_zone_threshold": 0.5, "possession_change_threshold": 3, "history_length": 100, "update_interval": 2.0}, "gui": {"window_width": 1000, "window_height": 700, "update_interval": 1500, "max_trail_length": 15, "default_trail_length": 8, "heatmap_resolution": [30, 50], "auto_save_interval": 120}, "logging": {"level": "WARNING", "format": "%(asctime)s - %(levelname)s - %(message)s", "file_path": null, "max_file_size": 1048576, "backup_count": 1, "console_output": true}, "environment": "low_performance", "debug": false, "data_directory": "./data", "temp_directory": "./temp", "max_concurrent_videos": 1, "cleanup_interval": 120}}