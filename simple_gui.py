#!/usr/bin/env python3
"""
Simple YOLO Tracking GUI (Fallback Version)
Works without Redis/Celery dependencies for basic functionality
"""

import cv2
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import threading
import time
from collections import defaultdict, deque
from datetime import datetime
import json
import os

try:
    from ultralytics import YOLO
    from deep_sort_realtime.deepsort_tracker import DeepSort
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    print("Warning: YOLO or DeepSort not available. Some features will be disabled.")

try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("Warning: PIL not available. Video display will be limited.")

class SimpleAnalyzerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Simple YOLO Tracking GUI")
        self.root.geometry("1200x800")
        
        # Performance settings
        self.frame_skip = int(os.getenv("FRAME_SKIP", "2"))  # Process every 2nd frame by default
        self.low_performance_mode = os.getenv("YOLO_ENV") == "low_performance"

        # Initialize models if available
        if YOLO_AVAILABLE:
            try:
                self.model = YOLO("yolo11n.pt")
                self.model.to('cpu')  # Force CPU usage

                # Use optimized tracker settings
                if self.low_performance_mode:
                    self.tracker = DeepSort(
                        max_age=15, n_init=2, nms_max_overlap=0.9,
                        embedder="mobilenet", half=False, max_cosine_distance=0.4, nn_budget=30
                    )
                else:
                    self.tracker = DeepSort(max_age=60, n_init=3, nms_max_overlap=1.0, embedder="mobilenet", half=True)

                self.models_loaded = True
                print(f"✓ Models loaded ({'Low Performance' if self.low_performance_mode else 'Standard'} mode)")
            except Exception as e:
                print(f"Failed to load models: {e}")
                self.models_loaded = False
        else:
            self.models_loaded = False
        
        # Video and tracking variables
        self.cap = None
        self.is_playing = False
        self.current_frame = None
        self.frame_count = 0
        self.tracking_data = defaultdict(list)
        self.heatmap_data = defaultdict(lambda: np.zeros((60, 100)))
        self.recent_positions = defaultdict(lambda: deque(maxlen=30))
        
        # Simple possession tracking
        self.possession_stats = {
            'team_a_frames': 0,
            'team_b_frames': 0,
            'total_frames': 0,
            'possession_changes': 0,
            'last_possession': None
        }
        
        # Video properties
        self.fps = 30
        self.width = 640
        self.height = 480
        
        self.setup_gui()
        
    def setup_gui(self):
        # Create main frames
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Control buttons
        ttk.Button(control_frame, text="Load Video", command=self.load_video).pack(side=tk.LEFT, padx=5)
        self.play_button = ttk.Button(control_frame, text="Play", command=self.toggle_playback)
        self.play_button.pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Reset", command=self.reset_analysis).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Save Data", command=self.save_data).pack(side=tk.LEFT, padx=5)
        
        # Status
        self.status_label = ttk.Label(control_frame, text="Ready - Load a video to start")
        self.status_label.pack(side=tk.RIGHT, padx=5)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, expand=True, padx=5)
        
        # Create notebook for tabs
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # Video tab
        video_frame = ttk.Frame(notebook)
        notebook.add(video_frame, text="Live Video")
        self.setup_video_tab(video_frame)
        
        # Simple possession tab
        possession_frame = ttk.Frame(notebook)
        notebook.add(possession_frame, text="Simple Possession")
        self.setup_simple_possession_tab(possession_frame)
        
        # Heatmap tab
        heatmap_frame = ttk.Frame(notebook)
        notebook.add(heatmap_frame, text="Heatmap")
        self.setup_heatmap_tab(heatmap_frame)
        
        # Statistics tab
        stats_frame = ttk.Frame(notebook)
        notebook.add(stats_frame, text="Statistics")
        self.setup_stats_tab(stats_frame)
        
    def setup_video_tab(self, parent):
        self.video_label = tk.Label(parent, bg='black', text="No video loaded")
        self.video_label.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
    def setup_simple_possession_tab(self, parent):
        # Simple possession display
        stats_frame = ttk.LabelFrame(parent, text="Basic Possession Statistics")
        stats_frame.pack(fill=tk.X, padx=10, pady=10)
        
        self.team_a_var = tk.StringVar(value="Team A: 0.0%")
        ttk.Label(stats_frame, textvariable=self.team_a_var, font=("Arial", 16)).pack(pady=5)
        
        self.team_b_var = tk.StringVar(value="Team B: 0.0%")
        ttk.Label(stats_frame, textvariable=self.team_b_var, font=("Arial", 16)).pack(pady=5)
        
        self.changes_var = tk.StringVar(value="Possession Changes: 0")
        ttk.Label(stats_frame, textvariable=self.changes_var).pack(pady=5)
        
        # Simple chart
        self.possession_fig = Figure(figsize=(8, 4), dpi=100)
        self.possession_ax = self.possession_fig.add_subplot(111)
        self.possession_canvas = FigureCanvasTkAgg(self.possession_fig, parent)
        self.possession_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
    def setup_heatmap_tab(self, parent):
        self.heatmap_fig = Figure(figsize=(10, 6), dpi=100)
        self.heatmap_ax = self.heatmap_fig.add_subplot(111)
        self.heatmap_canvas = FigureCanvasTkAgg(self.heatmap_fig, parent)
        self.heatmap_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
        # Controls
        controls_frame = ttk.Frame(parent)
        controls_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(controls_frame, text="Update Heatmap", command=self.update_heatmap).pack(side=tk.LEFT, padx=5)
        
    def setup_stats_tab(self, parent):
        self.stats_text = tk.Text(parent, wrap=tk.WORD, font=("Courier", 10))
        scrollbar = ttk.Scrollbar(parent, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
    def load_video(self):
        file_path = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[("Video files", "*.mp4 *.avi *.mov *.mkv"), ("All files", "*.*")]
        )
        
        if file_path:
            if self.cap:
                self.cap.release()
            
            self.cap = cv2.VideoCapture(file_path)
            if not self.cap.isOpened():
                messagebox.showerror("Error", "Could not open video file")
                return
            
            # Get video properties
            self.fps = int(self.cap.get(cv2.CAP_PROP_FPS))
            self.width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            self.height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            self.status_label.config(text=f"Video loaded: {self.width}x{self.height}, {self.fps}fps, {total_frames} frames")
            self.reset_analysis()
            
    def toggle_playback(self):
        if not self.cap:
            messagebox.showwarning("Warning", "Please load a video first")
            return
            
        if not self.models_loaded:
            messagebox.showwarning("Warning", "YOLO models not loaded. Install ultralytics and deep-sort-realtime packages.")
            return
            
        self.is_playing = not self.is_playing
        self.play_button.config(text="Pause" if self.is_playing else "Play")
        
        if self.is_playing:
            self.play_video()
            
    def play_video(self):
        if not self.is_playing or not self.cap:
            return
            
        ret, frame = self.cap.read()
        if not ret:
            self.is_playing = False
            self.play_button.config(text="Play")
            self.status_label.config(text="Video finished")
            return
            
        self.current_frame = frame.copy()

        # Only process every nth frame to reduce lag
        if self.models_loaded and self.frame_count % self.frame_skip == 0:
            # Resize frame for processing if in low performance mode
            if self.low_performance_mode:
                process_frame = cv2.resize(frame, (480, 360))
            else:
                process_frame = frame
            self.process_frame(process_frame)
        
        # Update video display
        self.update_video_display(frame)
        
        # Update progress
        total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        progress = (self.frame_count / total_frames) * 100
        self.progress_var.set(progress)
        
        self.frame_count += 1
        
        # Schedule next frame
        self.root.after(int(1000/self.fps), self.play_video)
        
    def process_frame(self, frame):
        try:
            # Run YOLO detection with performance optimizations
            conf_threshold = 0.7 if self.low_performance_mode else 0.4
            max_detections = 10 if self.low_performance_mode else 50

            results = self.model(frame, conf=conf_threshold, max_det=max_detections)[0]
            detections = []

            for data in results.boxes.data.tolist():
                x1, y1, x2, y2, conf, cls = data
                if int(cls) == 0:  # Person class
                    detections.append([int(x1), int(y1), int(x2 - x1), int(y2 - y1), conf, int(cls)])

            # Limit detections further if needed
            if self.low_performance_mode:
                detections = detections[:8]
            
            # Update tracker
            ds_detections = [[d[:4], d[4], d[5]] for d in detections]
            tracks = self.tracker.update_tracks(ds_detections, frame=frame)
            
            # Process tracks
            for track in tracks:
                if not track.is_confirmed():
                    continue
                
                track_id = track.track_id
                ltrb = track.to_ltrb()
                x1, y1, x2, y2 = map(int, ltrb)
                center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
                
                # Store tracking data
                position_data = {
                    'frame': self.frame_count,
                    'x': center_x,
                    'y': center_y,
                    'bbox': [x1, y1, x2-x1, y2-y1],
                    'confidence': track.det_conf if hasattr(track, 'det_conf') else 1.0
                }
                
                self.tracking_data[track_id].append(position_data)
                self.recent_positions[track_id].append((center_x, center_y))
                
                # Update heatmap
                field_x = int((center_x / self.width) * 100)
                field_y = int((center_y / self.height) * 60)
                field_x = max(0, min(99, field_x))
                field_y = max(0, min(59, field_y))
                self.heatmap_data[track_id][field_y, field_x] += 1
                
                # Draw on frame
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.putText(frame, f"ID: {track_id}", (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            # Simple possession calculation
            self.calculate_simple_possession(tracks)
            
            # Update displays every 30 frames
            if self.frame_count % 30 == 0:
                self.update_possession_display()
                self.update_statistics()
                
        except Exception as e:
            print(f"Frame processing error: {e}")
    
    def calculate_simple_possession(self, tracks):
        """Simple possession calculation based on player positions"""
        if not tracks:
            return
        
        team_a_count = 0
        team_b_count = 0
        
        for track in tracks:
            if not track.is_confirmed():
                continue
            
            ltrb = track.to_ltrb()
            center_x = (ltrb[0] + ltrb[2]) / 2
            
            # Simple zone division
            if center_x < self.width * 0.4:
                team_a_count += 1
            elif center_x > self.width * 0.6:
                team_b_count += 1
        
        # Determine possession
        current_possession = None
        if team_a_count > team_b_count:
            current_possession = 'A'
            self.possession_stats['team_a_frames'] += 1
        elif team_b_count > team_a_count:
            current_possession = 'B'
            self.possession_stats['team_b_frames'] += 1
        
        # Check for possession change
        if (self.possession_stats['last_possession'] and 
            self.possession_stats['last_possession'] != current_possession and
            current_possession is not None):
            self.possession_stats['possession_changes'] += 1
        
        self.possession_stats['last_possession'] = current_possession
        self.possession_stats['total_frames'] += 1

    def update_video_display(self, frame):
        if not PIL_AVAILABLE:
            return

        try:
            # Resize frame for display
            display_frame = cv2.resize(frame, (640, 480))
            display_frame = cv2.cvtColor(display_frame, cv2.COLOR_BGR2RGB)

            # Convert to PhotoImage
            image = Image.fromarray(display_frame)
            photo = ImageTk.PhotoImage(image)

            self.video_label.config(image=photo)
            self.video_label.image = photo  # Keep a reference
        except Exception as e:
            print(f"Video display error: {e}")

    def update_possession_display(self):
        """Update simple possession display"""
        try:
            total = self.possession_stats['total_frames']
            if total > 0:
                team_a_pct = (self.possession_stats['team_a_frames'] / total) * 100
                team_b_pct = (self.possession_stats['team_b_frames'] / total) * 100

                self.team_a_var.set(f"Team A: {team_a_pct:.1f}%")
                self.team_b_var.set(f"Team B: {team_b_pct:.1f}%")
                self.changes_var.set(f"Possession Changes: {self.possession_stats['possession_changes']}")

                # Update simple chart
                self.possession_ax.clear()
                teams = ['Team A', 'Team B', 'Neutral']
                percentages = [team_a_pct, team_b_pct, 100 - team_a_pct - team_b_pct]
                colors = ['blue', 'red', 'gray']

                self.possession_ax.pie(percentages, labels=teams, colors=colors, autopct='%1.1f%%')
                self.possession_ax.set_title('Possession Distribution')
                self.possession_canvas.draw()
        except Exception as e:
            print(f"Possession display error: {e}")

    def update_heatmap(self):
        """Update heatmap display"""
        try:
            self.heatmap_ax.clear()

            # Combine all player heatmaps
            combined_heatmap = np.zeros((60, 100))
            for track_id, heatmap in self.heatmap_data.items():
                combined_heatmap += heatmap

            if combined_heatmap.max() > 0:
                im = self.heatmap_ax.imshow(combined_heatmap, cmap='hot', interpolation='gaussian',
                                          extent=[0, 100, 0, 60], origin='lower', alpha=0.8)
                self.heatmap_fig.colorbar(im, ax=self.heatmap_ax, label='Activity Intensity')

            self.heatmap_ax.set_title('Player Activity Heatmap')
            self.heatmap_ax.set_xlabel('Field Width (%)')
            self.heatmap_ax.set_ylabel('Field Height (%)')
            self.heatmap_ax.grid(True, alpha=0.3)

            self.heatmap_canvas.draw()
        except Exception as e:
            print(f"Heatmap update error: {e}")

    def update_statistics(self):
        """Update statistics display"""
        try:
            stats_text = f"=== SIMPLE TRACKING STATISTICS ===\n"
            stats_text += f"Frame: {self.frame_count}\n"
            stats_text += f"Active Players: {len(self.recent_positions)}\n"
            stats_text += f"Total Tracked Players: {len(self.tracking_data)}\n\n"

            stats_text += "POSSESSION STATISTICS:\n"
            stats_text += "-" * 30 + "\n"
            total = self.possession_stats['total_frames']
            if total > 0:
                team_a_pct = (self.possession_stats['team_a_frames'] / total) * 100
                team_b_pct = (self.possession_stats['team_b_frames'] / total) * 100
                stats_text += f"Team A Possession: {team_a_pct:.1f}%\n"
                stats_text += f"Team B Possession: {team_b_pct:.1f}%\n"
                stats_text += f"Possession Changes: {self.possession_stats['possession_changes']}\n\n"

            stats_text += "PLAYER DETAILS:\n"
            stats_text += "-" * 30 + "\n"

            for track_id, positions in self.tracking_data.items():
                if len(positions) > 0:
                    latest_pos = positions[-1]
                    stats_text += f"Player {track_id}:\n"
                    stats_text += f"  Position: ({latest_pos['x']}, {latest_pos['y']})\n"
                    stats_text += f"  Frames tracked: {len(positions)}\n"
                    stats_text += f"  Confidence: {latest_pos['confidence']:.2f}\n\n"

            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_text)
        except Exception as e:
            print(f"Statistics update error: {e}")

    def reset_analysis(self):
        """Reset all analysis data"""
        self.tracking_data.clear()
        self.heatmap_data.clear()
        self.recent_positions.clear()
        self.frame_count = 0
        self.progress_var.set(0)

        # Reset possession stats
        self.possession_stats = {
            'team_a_frames': 0,
            'team_b_frames': 0,
            'total_frames': 0,
            'possession_changes': 0,
            'last_possession': None
        }

        # Reset tracker if available
        if self.models_loaded:
            try:
                self.tracker = DeepSort(max_age=60, n_init=3, nms_max_overlap=1.0, embedder="mobilenet", half=True)
            except:
                pass

        # Clear displays
        self.team_a_var.set("Team A: 0.0%")
        self.team_b_var.set("Team B: 0.0%")
        self.changes_var.set("Possession Changes: 0")

        if hasattr(self, 'heatmap_ax'):
            self.heatmap_ax.clear()
            self.heatmap_canvas.draw()

        if hasattr(self, 'possession_ax'):
            self.possession_ax.clear()
            self.possession_canvas.draw()

        if hasattr(self, 'stats_text'):
            self.stats_text.delete(1.0, tk.END)

        self.status_label.config(text="Analysis reset")

    def save_data(self):
        """Save analysis data"""
        if not self.tracking_data and not self.possession_stats['total_frames']:
            messagebox.showwarning("Warning", "No data to save")
            return

        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Save tracking data
            if self.tracking_data:
                tracking_filename = f"simple_tracking_data_{timestamp}.json"
                with open(tracking_filename, 'w') as f:
                    json_data = {str(k): v for k, v in self.tracking_data.items()}
                    json.dump(json_data, f, indent=2)

            # Save possession stats
            possession_filename = f"simple_possession_stats_{timestamp}.json"
            with open(possession_filename, 'w') as f:
                json.dump(self.possession_stats, f, indent=2)

            # Save visualizations
            if hasattr(self, 'heatmap_fig'):
                self.heatmap_fig.savefig(f"simple_heatmap_{timestamp}.png", dpi=300, bbox_inches='tight')

            if hasattr(self, 'possession_fig'):
                self.possession_fig.savefig(f"simple_possession_{timestamp}.png", dpi=300, bbox_inches='tight')

            messagebox.showinfo("Success", f"Data saved with timestamp {timestamp}")
            self.status_label.config(text=f"Data saved: {timestamp}")

        except Exception as e:
            print(f"Save error: {e}")
            messagebox.showerror("Error", f"Failed to save data: {e}")

def main():
    root = tk.Tk()
    app = SimpleAnalyzerGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
