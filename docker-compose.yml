version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: yolo_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    restart: unless-stopped

  celery_worker:
    build: .
    container_name: yolo_celery_worker
    command: celery -A celery_config worker --loglevel=info --concurrency=4 --queues=high_priority,medium_priority,low_priority
    volumes:
      - .:/app
      - ./data:/app/data
      - ./temp:/app/temp
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - YOLO_ENV=production
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  celery_beat:
    build: .
    container_name: yolo_celery_beat
    command: celery -A celery_config beat --loglevel=info
    volumes:
      - .:/app
      - ./data:/app/data
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - YOLO_ENV=production
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped

  flower:
    build: .
    container_name: yolo_flower
    command: celery -A celery_config flower --port=5555
    ports:
      - "5555:5555"
    environment:
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
    depends_on:
      redis:
        condition: service_healthy
    restart: unless-stopped

  gui_app:
    build: .
    container_name: yolo_gui
    command: python enhanced_gui_analyzer.py
    volumes:
      - .:/app
      - ./data:/app/data
      - ./temp:/app/temp
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    environment:
      - DISPLAY=${DISPLAY}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - YOLO_ENV=production
    depends_on:
      redis:
        condition: service_healthy
    network_mode: host
    restart: unless-stopped

volumes:
  redis_data:
    driver: local

networks:
  default:
    driver: bridge
