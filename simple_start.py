#!/usr/bin/env python3
"""
Simple Startup Script for Enhanced YOLO Tracking System
Handles missing dependencies and provides fallback options
"""

import os
import sys
import subprocess
import logging

def setup_basic_logging():
    """Setup basic logging without file handler"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )

def create_directories():
    """Create necessary directories"""
    directories = ['data', 'temp', 'logs']
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✓ Created directory: {directory}")

def check_redis():
    """Check if Redis is available"""
    try:
        import redis
        # Try to connect to Redis
        r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=5)
        r.ping()
        print("✓ Redis is running")
        return True
    except ImportError:
        print("✗ Redis package not installed")
        return False
    except Exception as e:
        print(f"✗ Redis not available: {e}")
        return False

def install_missing_packages():
    """Install missing packages"""
    try:
        print("Installing missing packages...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ Packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install packages: {e}")
        return False

def start_redis_fallback():
    """Try to start Redis using available methods"""
    print("Attempting to start Redis...")
    
    # Try Docker first
    try:
        subprocess.check_call(["docker", "run", "-d", "-p", "6379:6379", "--name", "yolo_redis", "redis:alpine"], 
                            stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("✓ Started Redis using Docker")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        pass
    
    # Try redis-server
    try:
        subprocess.Popen(["redis-server"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        print("✓ Started Redis server")
        return True
    except FileNotFoundError:
        pass
    
    print("✗ Could not start Redis. Please install Redis or Docker.")
    return False

def start_simple_gui():
    """Start the original simple GUI without Celery/Redis dependencies"""
    print("Starting simple GUI (without distributed processing)...")
    try:
        # Use the original realtime_gui_analyzer.py if enhanced version fails
        subprocess.run([sys.executable, "realtime_gui_analyzer.py"])
    except FileNotFoundError:
        print("✗ GUI file not found")
        return False
    except Exception as e:
        print(f"✗ Failed to start GUI: {e}")
        return False

def start_enhanced_system():
    """Start the enhanced system with all features"""
    print("Starting enhanced system...")
    try:
        from start_system import SystemManager
        manager = SystemManager()
        if manager.start_all():
            manager.wait()
        else:
            return False
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False
    except Exception as e:
        print(f"✗ Failed to start enhanced system: {e}")
        return False

def main():
    """Main startup logic with fallbacks"""
    print("="*60)
    print("Enhanced YOLO Tracking System - Simple Startup")
    print("="*60)
    
    # Setup basic logging
    setup_basic_logging()
    
    # Create directories
    create_directories()
    
    # Check if we can run the enhanced system
    redis_available = check_redis()
    
    if not redis_available:
        print("\nRedis not available. Attempting to start Redis...")
        redis_available = start_redis_fallback()
    
    # Try to start the enhanced system
    if redis_available:
        print("\n🚀 Starting enhanced system with all features...")
        try:
            if start_enhanced_system():
                return
        except Exception as e:
            print(f"Enhanced system failed: {e}")
    
    # Fallback to simple GUI
    print("\n🔄 Falling back to simple GUI mode...")
    print("Note: Advanced features (possession analytics, distributed processing) will not be available.")
    
    # Check if we have the basic requirements
    try:
        import cv2
        import numpy as np
        import matplotlib.pyplot as plt
        from ultralytics import YOLO
        print("✓ Basic requirements available")
    except ImportError as e:
        print(f"✗ Missing basic requirements: {e}")
        print("Installing basic requirements...")
        if not install_missing_packages():
            print("Failed to install requirements. Please install manually:")
            print("pip install ultralytics opencv-python numpy matplotlib")
            return
    
    # Start simple GUI
    start_simple_gui()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\nShutdown requested by user")
    except Exception as e:
        print(f"\nUnexpected error: {e}")
        print("Please check the logs and try again.")
        sys.exit(1)
