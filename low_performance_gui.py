#!/usr/bin/env python3
"""
Low Performance YOLO Tracking GUI
Optimized for low-end systems with reduced accuracy but maintained functionality
"""

import cv2
import numpy as np
import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
from matplotlib.figure import Figure
import threading
import time
from collections import defaultdict, deque
from datetime import datetime
import json
import os

try:
    from ultralytics import YOL<PERSON>
    from deep_sort_realtime.deepsort_tracker import DeepSort
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False
    print("Warning: YOLO or DeepSort not available. Some features will be disabled.")

try:
    from PIL import Image, ImageTk
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("Warning: PIL not available. Video display will be limited.")

class LowPerformanceGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Low Performance YOLO Tracking GUI")
        self.root.geometry("1000x700")
        
        # Performance settings
        self.frame_skip = 3  # Process every 3rd frame
        self.display_scale = 0.5  # Scale down display
        self.update_interval = 2000  # Update GUI every 2 seconds
        self.max_detections = 10  # Limit detections
        
        # Initialize models with low performance settings
        if YOLO_AVAILABLE:
            try:
                self.model = YOLO("yolo11n.pt")
                # Configure for CPU and low performance
                self.model.to('cpu')
                
                self.tracker = DeepSort(
                    max_age=15,  # Reduced from 60
                    n_init=2,    # Reduced from 3
                    nms_max_overlap=0.9,
                    embedder="mobilenet",
                    half=False,  # Disable half precision for CPU
                    max_cosine_distance=0.4,
                    nn_budget=30  # Reduced from 100
                )
                self.models_loaded = True
                print("✓ Models loaded with low performance settings")
            except Exception as e:
                print(f"Failed to load models: {e}")
                self.models_loaded = False
        else:
            self.models_loaded = False
        
        # Video and tracking variables
        self.cap = None
        self.is_playing = False
        self.current_frame = None
        self.frame_count = 0
        self.processed_frame_count = 0
        self.tracking_data = defaultdict(list)
        self.heatmap_data = defaultdict(lambda: np.zeros((30, 50)))  # Reduced resolution
        self.recent_positions = defaultdict(lambda: deque(maxlen=15))  # Reduced trail length
        
        # Simple possession tracking
        self.possession_stats = {
            'team_a_frames': 0,
            'team_b_frames': 0,
            'total_frames': 0,
            'possession_changes': 0,
            'last_possession': None
        }
        
        # Video properties
        self.fps = 15  # Reduced from 30
        self.width = 480  # Reduced resolution
        self.height = 360
        
        self.setup_gui()
        
    def setup_gui(self):
        # Create main frames
        control_frame = ttk.Frame(self.root)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Control buttons
        ttk.Button(control_frame, text="Load Video", command=self.load_video).pack(side=tk.LEFT, padx=5)
        self.play_button = ttk.Button(control_frame, text="Play", command=self.toggle_playback)
        self.play_button.pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Reset", command=self.reset_analysis).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Save Data", command=self.save_data).pack(side=tk.LEFT, padx=5)
        
        # Performance mode indicator
        perf_label = ttk.Label(control_frame, text="🔋 Low Performance Mode", foreground="orange")
        perf_label.pack(side=tk.LEFT, padx=10)
        
        # Status
        self.status_label = ttk.Label(control_frame, text="Ready - Load a video to start")
        self.status_label.pack(side=tk.RIGHT, padx=5)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(side=tk.RIGHT, padx=5)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Video tab
        self.video_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.video_frame, text="Video")
        
        # Video display (smaller size)
        self.video_label = ttk.Label(self.video_frame, text="No video loaded")
        self.video_label.pack(pady=10)
        
        # Statistics tab
        self.stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.stats_frame, text="Statistics")
        
        # Create statistics display
        self.setup_statistics_tab()
        
        # Heatmap tab (simplified)
        self.heatmap_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.heatmap_frame, text="Heatmap")
        
        # Create simplified heatmap
        self.setup_heatmap_tab()
        
    def setup_statistics_tab(self):
        # Statistics text widget
        self.stats_text = tk.Text(self.stats_frame, height=20, width=60)
        self.stats_text.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)
        
        scrollbar = ttk.Scrollbar(self.stats_frame, orient="vertical", command=self.stats_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.stats_text.config(yscrollcommand=scrollbar.set)
        
    def setup_heatmap_tab(self):
        # Simplified heatmap with lower resolution
        self.heatmap_fig = Figure(figsize=(6, 4), dpi=80)  # Reduced size
        self.heatmap_ax = self.heatmap_fig.add_subplot(111)
        self.heatmap_canvas = FigureCanvasTkAgg(self.heatmap_fig, self.heatmap_frame)
        self.heatmap_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
        
    def load_video(self):
        file_path = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[("Video files", "*.mp4 *.avi *.mov *.mkv"), ("All files", "*.*")]
        )
        
        if file_path:
            if self.cap:
                self.cap.release()
            
            self.cap = cv2.VideoCapture(file_path)
            
            if not self.cap.isOpened():
                messagebox.showerror("Error", "Could not open video file")
                return
            
            # Get video properties and reduce them
            original_fps = int(self.cap.get(cv2.CAP_PROP_FPS))
            self.fps = min(15, original_fps)  # Cap at 15 FPS
            
            original_width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            original_height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # Scale down resolution
            self.width = min(480, original_width)
            self.height = min(360, original_height)
            
            total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / original_fps
            
            self.status_label.config(text=f"Video loaded: {self.width}x{self.height} @ {self.fps}FPS, {duration:.1f}s")
            self.reset_analysis()
            
    def reset_analysis(self):
        self.frame_count = 0
        self.processed_frame_count = 0
        self.tracking_data.clear()
        self.heatmap_data.clear()
        self.recent_positions.clear()
        self.possession_stats = {
            'team_a_frames': 0,
            'team_b_frames': 0,
            'total_frames': 0,
            'possession_changes': 0,
            'last_possession': None
        }
        self.progress_var.set(0)
        
        if self.cap:
            self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
        
        self.status_label.config(text="Analysis reset")
        
    def toggle_playback(self):
        if not self.cap:
            messagebox.showwarning("Warning", "Please load a video first")
            return
            
        if not self.models_loaded:
            messagebox.showwarning("Warning", "YOLO models not loaded. Install ultralytics and deep-sort-realtime packages.")
            return
            
        self.is_playing = not self.is_playing
        self.play_button.config(text="Pause" if self.is_playing else "Play")
        
        if self.is_playing:
            self.play_video()
            
    def play_video(self):
        if not self.is_playing or not self.cap:
            return
            
        ret, frame = self.cap.read()
        if not ret:
            self.is_playing = False
            self.play_button.config(text="Play")
            self.status_label.config(text="Video finished")
            return
            
        # Resize frame immediately to reduce processing load
        frame = cv2.resize(frame, (self.width, self.height))
        self.current_frame = frame.copy()
        
        # Only process every nth frame to reduce load
        if self.models_loaded and self.frame_count % self.frame_skip == 0:
            self.process_frame(frame)
        
        # Update video display
        self.update_video_display(frame)
        
        # Update progress
        total_frames = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
        progress = (self.frame_count / total_frames) * 100
        self.progress_var.set(progress)
        
        self.frame_count += 1
        
        # Schedule next frame with longer delay
        self.root.after(int(1000/self.fps * 1.5), self.play_video)  # 1.5x slower
        
    def process_frame(self, frame):
        try:
            # Run YOLO detection with higher confidence threshold
            results = self.model(frame, conf=0.6, max_det=self.max_detections)[0]
            detections = []
            
            for data in results.boxes.data.tolist():
                x1, y1, x2, y2, conf, cls = data
                if int(cls) == 0:  # Person class only
                    detections.append([int(x1), int(y1), int(x2 - x1), int(y2 - y1), conf, int(cls)])
            
            # Limit detections to reduce processing
            detections = detections[:self.max_detections]
            
            # Update tracker
            ds_detections = [[d[:4], d[4], d[5]] for d in detections]
            tracks = self.tracker.update_tracks(ds_detections, frame=frame)
            
            # Process tracks (simplified)
            for track in tracks:
                if not track.is_confirmed():
                    continue
                
                track_id = track.track_id
                ltrb = track.to_ltrb()
                x1, y1, x2, y2 = map(int, ltrb)
                center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
                
                # Store minimal tracking data
                self.tracking_data[track_id].append({
                    'frame': self.processed_frame_count,
                    'x': center_x,
                    'y': center_y
                })
                
                self.recent_positions[track_id].append((center_x, center_y))
                
                # Update heatmap with lower resolution
                field_x = int((center_x / self.width) * 50)
                field_y = int((center_y / self.height) * 30)
                field_x = max(0, min(49, field_x))
                field_y = max(0, min(29, field_y))
                self.heatmap_data[track_id][field_y, field_x] += 1
                
                # Draw on frame (simplified)
                cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 255, 0), 1)
                cv2.putText(frame, f"{track_id}", (x1, y1-5), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (0, 255, 0), 1)
            
            self.processed_frame_count += 1
            
            # Update displays less frequently
            if self.processed_frame_count % 10 == 0:
                self.update_statistics()
                
        except Exception as e:
            print(f"Frame processing error: {e}")
    
    def update_video_display(self, frame):
        if not PIL_AVAILABLE:
            return
            
        try:
            # Further scale down for display
            display_frame = cv2.resize(frame, (int(self.width * self.display_scale), 
                                             int(self.height * self.display_scale)))
            display_frame = cv2.cvtColor(display_frame, cv2.COLOR_BGR2RGB)
            
            # Convert to PhotoImage
            image = Image.fromarray(display_frame)
            photo = ImageTk.PhotoImage(image)
            
            self.video_label.config(image=photo)
            self.video_label.image = photo  # Keep a reference
        except Exception as e:
            print(f"Display update error: {e}")
    
    def update_statistics(self):
        """Update statistics display with minimal processing"""
        try:
            stats_text = f"""=== LOW PERFORMANCE MODE STATISTICS ===
Frame Skip: Every {self.frame_skip} frames
Processed Frames: {self.processed_frame_count}
Total Frames: {self.frame_count}
Active Tracks: {len(self.tracking_data)}
Resolution: {self.width}x{self.height}
FPS: {self.fps}

=== TRACKING DATA ===
"""
            
            # Add basic track info
            for track_id, positions in list(self.tracking_data.items())[-5:]:  # Show last 5 tracks
                if positions:
                    last_pos = positions[-1]
                    stats_text += f"Track {track_id}: Frame {last_pos['frame']}, Pos ({last_pos['x']}, {last_pos['y']})\n"
            
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_text)
            
        except Exception as e:
            print(f"Statistics update error: {e}")
    
    def update_heatmap(self):
        """Update heatmap with reduced frequency and resolution"""
        try:
            self.heatmap_ax.clear()
            
            # Combine all player heatmaps
            combined_heatmap = np.zeros((30, 50))  # Lower resolution
            for track_id, heatmap in self.heatmap_data.items():
                combined_heatmap += heatmap
            
            if combined_heatmap.max() > 0:
                im = self.heatmap_ax.imshow(combined_heatmap, cmap='hot', interpolation='nearest',
                                          extent=[0, 50, 0, 30], origin='lower', alpha=0.8)
                self.heatmap_ax.set_title('Player Activity Heatmap (Low Res)')
                self.heatmap_ax.set_xlabel('Field Width')
                self.heatmap_ax.set_ylabel('Field Height')
            else:
                self.heatmap_ax.text(0.5, 0.5, 'No tracking data yet', 
                                   transform=self.heatmap_ax.transAxes, ha='center', va='center')
            
            self.heatmap_canvas.draw()
            
        except Exception as e:
            print(f"Heatmap update error: {e}")
    
    def save_data(self):
        """Save tracking data and visualizations"""
        if not self.tracking_data:
            messagebox.showwarning("Warning", "No tracking data to save")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save tracking data
        data_to_save = {
            'metadata': {
                'timestamp': timestamp,
                'total_frames': self.frame_count,
                'processed_frames': self.processed_frame_count,
                'frame_skip': self.frame_skip,
                'resolution': f"{self.width}x{self.height}",
                'fps': self.fps,
                'performance_mode': 'low'
            },
            'tracking_data': dict(self.tracking_data),
            'possession_stats': self.possession_stats
        }
        
        filename = f"tracking_data_low_perf_{timestamp}.json"
        with open(filename, 'w') as f:
            json.dump(data_to_save, f, indent=2)
        
        # Save heatmap
        if hasattr(self, 'heatmap_fig'):
            self.heatmap_fig.savefig(f"heatmap_low_perf_{timestamp}.png", dpi=150, bbox_inches='tight')
        
        messagebox.showinfo("Success", f"Data saved with timestamp {timestamp}")
        self.status_label.config(text=f"Data saved: {timestamp}")

def main():
    if not YOLO_AVAILABLE:
        print("Error: YOLO not available. Please install required packages:")
        print("pip install ultralytics deep-sort-realtime")
        return
    
    root = tk.Tk()
    app = LowPerformanceGUI(root)
    
    # Schedule periodic updates
    def periodic_update():
        if hasattr(app, 'heatmap_canvas'):
            app.update_heatmap()
        root.after(app.update_interval, periodic_update)
    
    root.after(app.update_interval, periodic_update)
    root.mainloop()

if __name__ == "__main__":
    main()
