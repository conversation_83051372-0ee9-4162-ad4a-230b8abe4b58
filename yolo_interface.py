import cv2
import numpy as np
import matplotlib.pyplot as plt
from collections import defaultdict
from ultralytics import YOL<PERSON>
from deep_sort_realtime.deepsort_tracker import DeepSort

# Load YOLO model
model = YOLO("yolo11n.pt")

# Initialize DeepSort tracker
tracker = DeepSort(max_age=60, n_init=3, nms_max_overlap=1.0, embedder="mobilenet", half=True)

# Open input video
input_path = 'input_video/test.mp4'
cap = cv2.VideoCapture(input_path)

# Get video properties
fps = int(cap.get(cv2.CAP_PROP_FPS))
width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

# Data collection
tracking_data = defaultdict(list)  # {track_id: [(frame, x, y, w, h, conf), ...]}
frame_count = 0

while True:
    ret, frame = cap.read()
    if not ret:
        break

    # Run YOLO detection
    results = model(frame)[0]
    detections = []
    for data in results.boxes.data.tolist():
        x1, y1, x2, y2, conf, cls = data
        if int(cls) == 0 and conf > 0.4:  # Only person class
            detections.append([int(x1), int(y1), int(x2 - x1), int(y2 - y1), conf, int(cls)])

    # Update tracker
    ds_detections = [[d[:4], d[4], d[5]] for d in detections]
    tracks = tracker.update_tracks(ds_detections, frame=frame)

    # Collect tracking data
    for track in tracks:
        if not track.is_confirmed():
            continue
        track_id = track.track_id
        ltrb = track.to_ltrb()
        x1, y1, x2, y2 = map(int, ltrb)
        center_x, center_y = (x1 + x2) // 2, (y1 + y2) // 2
        
        tracking_data[track_id].append({
            'frame': frame_count,
            'x': center_x,
            'y': center_y,
            'bbox': [x1, y1, x2-x1, y2-y1],
            'confidence': track.det_conf if hasattr(track, 'det_conf') else 1.0
        })
    
    frame_count += 1

cap.release()

# Generate map view
def create_field_map():
    plt.figure(figsize=(12, 8))
    
    # Draw football field outline (normalized coordinates)
    field_width, field_height = 100, 60
    plt.xlim(0, field_width)
    plt.ylim(0, field_height)
    
    # Plot player trajectories
    colors = plt.cm.tab20(np.linspace(0, 1, len(tracking_data)))
    
    for i, (track_id, positions) in enumerate(tracking_data.items()):
        if len(positions) < 10:  # Filter short tracks
            continue
            
        # Convert pixel coordinates to field coordinates
        x_coords = [pos['x'] / width * field_width for pos in positions]
        y_coords = [pos['y'] / height * field_height for pos in positions]
        
        plt.plot(x_coords, y_coords, color=colors[i], alpha=0.7, linewidth=2, label=f'Player {track_id}')
        plt.scatter(x_coords[-1], y_coords[-1], color=colors[i], s=100, marker='o')  # Final position
    
    plt.title('Player Movement Map')
    plt.xlabel('Field Width')
    plt.ylabel('Field Height')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig('player_movement_map.png', dpi=300, bbox_inches='tight')
    plt.show()

# Save tracking data
import json
with open('tracking_data.json', 'w') as f:
    json.dump(dict(tracking_data), f, indent=2)

create_field_map()
print(f"Tracked {len(tracking_data)} players across {frame_count} frames")
print("Data saved to tracking_data.json and map saved to player_movement_map.png")
