@echo off
echo 🔋 Starting Optimized YOLO Tracker...
echo =====================================

REM Set performance environment variables
set YOLO_ENV=low_performance
set OPENCV_NUM_THREADS=1
set OMP_NUM_THREADS=1
set MKL_NUM_THREADS=1
set TORCH_NUM_THREADS=1

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ✗ Python not found. Please install Python 3.8 or higher.
    pause
    exit /b 1
)

echo ✓ Python found

REM Try to run the optimized version
echo 🚀 Launching low performance GUI...
python low_performance_gui.py

if errorlevel 1 (
    echo ⚠️  Low performance GUI failed, trying alternatives...
    
    echo 🔄 Trying simple GUI...
    python simple_gui.py
    
    if errorlevel 1 (
        echo 🔄 Trying command line version...
        python yolo_interface.py
    )
)

echo ✅ Done
pause
