# Enhanced Real-Time YOLO Tracking & Possession Analysis System

This project provides a comprehensive, production-ready real-time system for YOLO-based object tracking with advanced visualization features, possession analytics, and robust distributed processing using Celery and Redis.

## Features

### 🎥 Real-Time Video Processing

- Live YOLO object detection and tracking
- DeepSort integration for consistent ID tracking
- Real-time video display with bounding boxes and IDs

### 🔥 Heatmap Analysis

- Dynamic heatmap generation showing player activity zones
- Real-time updates as video plays
- Customizable visualization options
- Export capabilities for analysis

### 🦅 <PERSON>'s Eye View

- Top-down field view with player positions
- Real-time movement trails
- Configurable trail length
- Field overlay with proper proportions

### 📊 Live Statistics

- Real-time tracking statistics
- Distance traveled calculations
- Average speed analysis
- Player activity metrics

### 💾 Data Export

- Save tracking data as JSON
- Export heatmap visualizations
- Export bird's eye view images
- Timestamped file naming

## Installation

1. Install required dependencies:

```bash
pip install -r requirements.txt
```

2. Ensure you have the YOLO model file:

- The system expects `yolo11n.pt` in the project directory
- Download from Ultralytics if not present

## Usage

### GUI Mode (Recommended)

```bash
python run_analyzer.py --mode gui
```

Or directly:

```bash
python realtime_gui_analyzer.py
```

### Original Batch Mode

```bash
python run_analyzer.py --mode batch
```

## GUI Interface

### Main Tabs

1. **Live Video**: Real-time video display with tracking overlays
2. **Heatmap Analysis**: Dynamic heatmap showing activity zones
3. **Bird's Eye View**: Top-down tracking visualization
4. **Statistics**: Live tracking metrics and player data

### Controls

- **Load Video**: Select video file for analysis
- **Play/Pause**: Control video playback
- **Reset**: Clear all tracking data and restart analysis
- **Save Data**: Export all tracking data and visualizations

### Heatmap Controls

- **Heatmap Type**: Choose between "All Players" or "Individual Players"
- **Update Heatmap**: Manually refresh heatmap visualization

### Bird's Eye View Controls

- **Trail Length**: Adjust the length of movement trails (5-100 frames)
- **Show Trails**: Toggle trail visibility

## File Structure

```
sena_project/
├── realtime_gui_analyzer.py    # Main GUI application
├── yolo_interface.py           # Original batch processing
├── run_analyzer.py             # Launcher script
├── requirements.txt            # Dependencies
├── README.md                   # This file
├── yolo11n.pt                 # YOLO model weights
└── input_video/               # Video files directory
    └── test.mp4
```

## Output Files

The system generates timestamped output files:

- `tracking_data_YYYYMMDD_HHMMSS.json`: Complete tracking data
- `heatmap_data_YYYYMMDD_HHMMSS.json`: Heatmap activity data
- `heatmap_YYYYMMDD_HHMMSS.png`: Heatmap visualization
- `birds_eye_view_YYYYMMDD_HHMMSS.png`: Bird's eye view image

## Technical Details

### Tracking System

- **YOLO Model**: YOLOv11 nano for fast detection
- **Tracker**: DeepSort for consistent ID assignment
- **Detection Threshold**: 0.4 confidence for person class
- **Tracking Parameters**: 60 frame max age, 3 frame initialization

### Visualization

- **Heatmap Resolution**: 100x60 grid (field proportions)
- **Update Frequency**: Every 10 frames for smooth performance
- **Trail Memory**: Configurable (default 30 frames)
- **Color Coding**: Unique colors per tracked ID

### Performance

- **Real-time Processing**: Optimized for live video analysis
- **Memory Management**: Efficient data structures for long videos
- **GUI Responsiveness**: Non-blocking video processing

## Troubleshooting

### Common Issues

1. **Video won't load**: Ensure video format is supported (MP4, AVI, MOV, MKV)
2. **Slow performance**: Try reducing video resolution or frame rate
3. **Missing dependencies**: Run `pip install -r requirements.txt`
4. **YOLO model not found**: Download `yolo11n.pt` from Ultralytics

### System Requirements

- **Python**: 3.8 or higher
- **RAM**: 4GB minimum, 8GB recommended
- **GPU**: Optional but recommended for better performance
- **OS**: Windows, macOS, or Linux

## Future Enhancements

- [ ] Multi-camera support
- [ ] Advanced analytics (formations, heat zones)
- [ ] Real-time streaming input
- [ ] Custom field templates
- [ ] Player identification and jersey numbers
- [ ] Team-based analysis

## License

This project is for educational and research purposes. Please ensure you have appropriate rights for any video content used.
