"""
Celery Configuration for YOLO Tracking System
Provides task queue management and prioritization
"""

from celery import Celery
import os
from kombu import Queue, Exchange

# Redis configuration
REDIS_HOST = os.getenv('REDIS_HOST', 'localhost')
REDIS_PORT = os.getenv('REDIS_PORT', 6379)
REDIS_DB = os.getenv('REDIS_DB', 0)
REDIS_PASSWORD = os.getenv('REDIS_PASSWORD', None)

# Construct Redis URL
if REDIS_PASSWORD:
    REDIS_URL = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
else:
    REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"

# Create Celery app
app = Celery('yolo_tracker')

# Celery configuration
app.conf.update(
    # Broker settings
    broker_url=REDIS_URL,
    result_backend=REDIS_URL,
    
    # Task settings
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    
    # Worker settings
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=1000,
    
    # Task routing and priorities
    task_routes={
        'yolo_tracker.tasks.process_frame': {'queue': 'high_priority'},
        'yolo_tracker.tasks.update_heatmap': {'queue': 'medium_priority'},
        'yolo_tracker.tasks.calculate_possession': {'queue': 'high_priority'},
        'yolo_tracker.tasks.save_data': {'queue': 'low_priority'},
        'yolo_tracker.tasks.generate_statistics': {'queue': 'medium_priority'},
    },
    
    # Queue definitions with priorities
    task_default_queue='medium_priority',
    task_queues=(
        Queue('high_priority', Exchange('high_priority'), routing_key='high_priority',
              queue_arguments={'x-max-priority': 10}),
        Queue('medium_priority', Exchange('medium_priority'), routing_key='medium_priority',
              queue_arguments={'x-max-priority': 5}),
        Queue('low_priority', Exchange('low_priority'), routing_key='low_priority',
              queue_arguments={'x-max-priority': 1}),
    ),
    
    # Result backend settings
    result_expires=3600,  # 1 hour
    result_backend_transport_options={
        'master_name': 'mymaster',
        'visibility_timeout': 3600,
    },
    
    # Monitoring
    worker_send_task_events=True,
    task_send_sent_event=True,
    
    # Error handling
    task_reject_on_worker_lost=True,
    task_ignore_result=False,
    
    # Beat schedule for periodic tasks
    beat_schedule={
        'cleanup-old-data': {
            'task': 'yolo_tracker.tasks.cleanup_old_data',
            'schedule': 300.0,  # Every 5 minutes
            'options': {'queue': 'low_priority'}
        },
        'update-possession-stats': {
            'task': 'yolo_tracker.tasks.update_possession_stats',
            'schedule': 1.0,  # Every second
            'options': {'queue': 'high_priority'}
        },
    },
)

# Task priority levels
class TaskPriority:
    CRITICAL = 10
    HIGH = 8
    MEDIUM = 5
    LOW = 2
    BACKGROUND = 1

# Auto-discover tasks
app.autodiscover_tasks(['yolo_tracker'])

if __name__ == '__main__':
    app.start()
