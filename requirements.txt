ultralytics>=8.0.0
opencv-python>=4.5.0
numpy>=1.21.0
matplotlib>=3.5.0
Pillow>=8.0.0
deep-sort-realtime>=1.2.0
torch>=1.9.0
torchvision>=0.10.0
celery>=5.3.0
redis>=4.5.0
kombu>=5.3.0D:\sena_project>python optimize_performance.py
🔋 YOLO Tracker Performance Optimizer
========================================
💻 Checking system resources...
RAM: 7GB total, 1GB available
CPU: 8 cores
⚠️  Low memory detected. Consider closing other applications.
🔧 Setting performance environment variables...
✓ Environment variables set for low performance mode
📝 Creating optimized configuration...
✓ Optimized config saved to config_optimized.json
🎥 Applying OpenCV optimizations...
✓ OpenCL disabled for better CPU performance
✓ OpenCV optimized for single-threaded performance
🚀 Creating optimized launch script...
Traceback (most recent call last):
  File "D:\sena_project\optimize_performance.py", line 214, in <module>     
    main()
  File "D:\sena_project\optimize_performance.py", line 195, in main
    create_launch_script()
  File "D:\sena_project\optimize_performance.py", line 170, in create_launch_script
    f.write(script_content)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f50b' in position 319: character maps to <undefined>

billiard>=4.1.0
vine>=5.0.0
click>=8.0.0
pytz>=2023.3
eventlet>=0.33.0
scikit-learn>=1.3.0
scipy>=1.10.0
flower>=2.0.0
