#!/usr/bin/env python3
"""
YOLO Tracking Analyzer Launcher
Choose between the original batch processing or the new real-time GUI
"""

import sys
import argparse

def main():
    parser = argparse.ArgumentParser(description="YOLO Tracking Analyzer")
    parser.add_argument("--mode", choices=["batch", "gui"], default="gui",
                       help="Choose processing mode: 'batch' for original processing, 'gui' for real-time GUI")
    
    args = parser.parse_args()
    
    if args.mode == "batch":
        print("Running original batch processing...")
        import yolo_interface
    elif args.mode == "gui":
        print("Starting real-time GUI analyzer...")
        from realtime_gui_analyzer import main as gui_main
        gui_main()

if __name__ == "__main__":
    main()
