#!/usr/bin/env python3
"""
YOLO Tracking Analyzer Launcher
Choose between the original batch processing or the new real-time GUI
"""

import sys
import argparse

def main():
    parser = argparse.ArgumentParser(description="YOLO Tracking Analyzer")
    parser.add_argument("--mode", choices=["batch", "gui", "low-perf"], default="gui",
                       help="Choose processing mode: 'batch' for original processing, 'gui' for real-time GUI, 'low-perf' for optimized low performance mode")
    parser.add_argument("--optimize", action="store_true",
                       help="Run performance optimization first")

    args = parser.parse_args()

    # Run optimization if requested
    if args.optimize:
        print("🔧 Running performance optimization...")
        import optimize_performance
        optimize_performance.main()
        print()

    if args.mode == "batch":
        print("Running original batch processing...")
        import yolo_interface
    elif args.mode == "gui":
        print("Starting real-time GUI analyzer...")
        from realtime_gui_analyzer import main as gui_main
        gui_main()
    elif args.mode == "low-perf":
        print("🔋 Starting low performance mode...")
        try:
            from low_performance_gui import main as low_perf_main
            low_perf_main()
        except ImportError:
            print("Low performance GUI not available, falling back to simple GUI with optimizations...")
            import os
            os.environ["YOLO_ENV"] = "low_performance"
            os.environ["FRAME_SKIP"] = "3"
            from simple_gui import main as simple_main
            simple_main()

if __name__ == "__main__":
    main()
