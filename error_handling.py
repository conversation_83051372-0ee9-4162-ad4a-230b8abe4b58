"""
Robust Error Handling and Recovery System
Provides comprehensive error handling, retry mechanisms, and system recovery
"""

import logging
import time
import traceback
import functools
import threading
from typing import Callable, Any, Optional, Dict, List
from dataclasses import dataclass
from enum import Enum
from collections import defaultdict, deque
import redis
from celery import Task
from celery.exceptions import Retry, WorkerLostError, Ignore
from config import config

logger = logging.getLogger(__name__)

class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """Error categories for classification"""
    NETWORK = "network"
    PROCESSING = "processing"
    STORAGE = "storage"
    VALIDATION = "validation"
    SYSTEM = "system"
    USER = "user"

@dataclass
class ErrorInfo:
    """Error information container"""
    timestamp: float
    error_type: str
    message: str
    severity: ErrorSeverity
    category: ErrorCategory
    traceback: str
    context: Dict[str, Any]
    retry_count: int = 0
    resolved: bool = False

class ErrorTracker:
    """Tracks and analyzes system errors"""
    
    def __init__(self, max_errors: int = 1000):
        self.max_errors = max_errors
        self.errors = deque(maxlen=max_errors)
        self.error_counts = defaultdict(int)
        self.lock = threading.Lock()
        
    def record_error(self, error: Exception, severity: ErrorSeverity, 
                    category: ErrorCategory, context: Dict[str, Any] = None) -> ErrorInfo:
        """Record an error occurrence"""
        error_info = ErrorInfo(
            timestamp=time.time(),
            error_type=type(error).__name__,
            message=str(error),
            severity=severity,
            category=category,
            traceback=traceback.format_exc(),
            context=context or {}
        )
        
        with self.lock:
            self.errors.append(error_info)
            self.error_counts[error_info.error_type] += 1
            
        logger.error(f"Error recorded: {error_info.error_type} - {error_info.message}")
        return error_info
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics"""
        with self.lock:
            recent_errors = [e for e in self.errors if time.time() - e.timestamp < 3600]  # Last hour
            
            return {
                "total_errors": len(self.errors),
                "recent_errors": len(recent_errors),
                "error_types": dict(self.error_counts),
                "severity_distribution": {
                    severity.value: sum(1 for e in recent_errors if e.severity == severity)
                    for severity in ErrorSeverity
                },
                "category_distribution": {
                    category.value: sum(1 for e in recent_errors if e.category == category)
                    for category in ErrorCategory
                }
            }
    
    def get_recent_errors(self, limit: int = 10) -> List[ErrorInfo]:
        """Get recent errors"""
        with self.lock:
            return list(self.errors)[-limit:]

# Global error tracker
error_tracker = ErrorTracker()

class RetryConfig:
    """Retry configuration for different error types"""
    
    CONFIGS = {
        ErrorCategory.NETWORK: {
            "max_retries": 5,
            "base_delay": 1.0,
            "max_delay": 60.0,
            "exponential_base": 2.0,
            "jitter": True
        },
        ErrorCategory.PROCESSING: {
            "max_retries": 3,
            "base_delay": 0.5,
            "max_delay": 10.0,
            "exponential_base": 1.5,
            "jitter": False
        },
        ErrorCategory.STORAGE: {
            "max_retries": 4,
            "base_delay": 2.0,
            "max_delay": 30.0,
            "exponential_base": 2.0,
            "jitter": True
        },
        ErrorCategory.VALIDATION: {
            "max_retries": 1,
            "base_delay": 0.1,
            "max_delay": 1.0,
            "exponential_base": 1.0,
            "jitter": False
        },
        ErrorCategory.SYSTEM: {
            "max_retries": 2,
            "base_delay": 5.0,
            "max_delay": 120.0,
            "exponential_base": 3.0,
            "jitter": True
        }
    }
    
    @classmethod
    def get_config(cls, category: ErrorCategory) -> Dict[str, Any]:
        """Get retry configuration for error category"""
        return cls.CONFIGS.get(category, cls.CONFIGS[ErrorCategory.PROCESSING])

def calculate_retry_delay(attempt: int, category: ErrorCategory) -> float:
    """Calculate retry delay with exponential backoff"""
    config = RetryConfig.get_config(category)
    
    base_delay = config["base_delay"]
    max_delay = config["max_delay"]
    exponential_base = config["exponential_base"]
    jitter = config["jitter"]
    
    # Exponential backoff
    delay = base_delay * (exponential_base ** (attempt - 1))
    delay = min(delay, max_delay)
    
    # Add jitter to prevent thundering herd
    if jitter:
        import random
        delay *= (0.5 + random.random() * 0.5)
    
    return delay

def robust_retry(category: ErrorCategory = ErrorCategory.PROCESSING, 
                severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                context: Dict[str, Any] = None):
    """Decorator for robust retry with error tracking"""
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            config = RetryConfig.get_config(category)
            max_retries = config["max_retries"]
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                    
                except Exception as e:
                    # Record error
                    error_info = error_tracker.record_error(
                        e, severity, category, 
                        {**(context or {}), "attempt": attempt, "function": func.__name__}
                    )
                    
                    # Check if we should retry
                    if attempt >= max_retries:
                        logger.error(f"Function {func.__name__} failed after {max_retries} retries")
                        raise
                    
                    # Calculate delay and wait
                    delay = calculate_retry_delay(attempt + 1, category)
                    logger.warning(f"Retrying {func.__name__} in {delay:.2f}s (attempt {attempt + 1}/{max_retries})")
                    time.sleep(delay)
            
        return wrapper
    return decorator

class RobustTask(Task):
    """Enhanced Celery task with robust error handling"""
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """Handle task failure"""
        # Determine error category and severity
        category = self._classify_error(exc)
        severity = self._determine_severity(exc, category)
        
        # Record error
        error_tracker.record_error(
            exc, severity, category,
            {
                "task_id": task_id,
                "task_name": self.name,
                "args": str(args)[:200],  # Truncate for storage
                "kwargs": str(kwargs)[:200]
            }
        )
        
        # Check if we should retry
        if self._should_retry(exc, category):
            retry_delay = calculate_retry_delay(self.request.retries + 1, category)
            raise self.retry(countdown=retry_delay, max_retries=RetryConfig.get_config(category)["max_retries"])
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """Handle task retry"""
        logger.warning(f"Task {self.name} retrying: {exc}")
    
    def on_success(self, retval, task_id, args, kwargs):
        """Handle task success"""
        if self.request.retries > 0:
            logger.info(f"Task {self.name} succeeded after {self.request.retries} retries")
    
    def _classify_error(self, exc: Exception) -> ErrorCategory:
        """Classify error by type"""
        error_type = type(exc).__name__
        
        network_errors = ["ConnectionError", "TimeoutError", "RedisConnectionError", "BrokenPipeError"]
        processing_errors = ["ValueError", "TypeError", "AttributeError", "IndexError"]
        storage_errors = ["IOError", "OSError", "PermissionError", "FileNotFoundError"]
        validation_errors = ["ValidationError", "AssertionError"]
        system_errors = ["MemoryError", "SystemError", "RuntimeError"]
        
        if error_type in network_errors:
            return ErrorCategory.NETWORK
        elif error_type in processing_errors:
            return ErrorCategory.PROCESSING
        elif error_type in storage_errors:
            return ErrorCategory.STORAGE
        elif error_type in validation_errors:
            return ErrorCategory.VALIDATION
        elif error_type in system_errors:
            return ErrorCategory.SYSTEM
        else:
            return ErrorCategory.PROCESSING  # Default
    
    def _determine_severity(self, exc: Exception, category: ErrorCategory) -> ErrorSeverity:
        """Determine error severity"""
        error_type = type(exc).__name__
        
        critical_errors = ["MemoryError", "SystemError", "KeyboardInterrupt"]
        high_errors = ["ConnectionError", "PermissionError", "FileNotFoundError"]
        
        if error_type in critical_errors:
            return ErrorSeverity.CRITICAL
        elif error_type in high_errors:
            return ErrorSeverity.HIGH
        elif category in [ErrorCategory.NETWORK, ErrorCategory.STORAGE]:
            return ErrorSeverity.MEDIUM
        else:
            return ErrorSeverity.LOW
    
    def _should_retry(self, exc: Exception, category: ErrorCategory) -> bool:
        """Determine if task should be retried"""
        # Don't retry validation errors or critical system errors
        if category == ErrorCategory.VALIDATION:
            return False
        
        error_type = type(exc).__name__
        non_retryable = ["KeyboardInterrupt", "SystemExit", "MemoryError"]
        
        return error_type not in non_retryable

class CircuitBreaker:
    """Circuit breaker pattern for preventing cascade failures"""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60, expected_exception: type = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self.lock = threading.Lock()
    
    def __call__(self, func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            with self.lock:
                if self.state == "OPEN":
                    if time.time() - self.last_failure_time > self.recovery_timeout:
                        self.state = "HALF_OPEN"
                        logger.info(f"Circuit breaker for {func.__name__} entering HALF_OPEN state")
                    else:
                        raise Exception(f"Circuit breaker OPEN for {func.__name__}")
                
                try:
                    result = func(*args, **kwargs)
                    
                    # Success - reset failure count
                    if self.state == "HALF_OPEN":
                        self.state = "CLOSED"
                        logger.info(f"Circuit breaker for {func.__name__} reset to CLOSED")
                    
                    self.failure_count = 0
                    return result
                    
                except self.expected_exception as e:
                    self.failure_count += 1
                    self.last_failure_time = time.time()
                    
                    if self.failure_count >= self.failure_threshold:
                        self.state = "OPEN"
                        logger.warning(f"Circuit breaker OPEN for {func.__name__} after {self.failure_count} failures")
                    
                    raise
        
        return wrapper

class HealthChecker:
    """System health monitoring and recovery"""
    
    def __init__(self):
        self.checks = {}
        self.last_check_time = {}
        self.check_interval = 30  # seconds
        
    def register_check(self, name: str, check_func: Callable[[], bool], interval: int = None):
        """Register a health check function"""
        self.checks[name] = check_func
        self.last_check_time[name] = 0
        if interval:
            self.check_interval = interval
    
    def run_checks(self) -> Dict[str, Any]:
        """Run all health checks"""
        results = {}
        current_time = time.time()
        
        for name, check_func in self.checks.items():
            if current_time - self.last_check_time[name] >= self.check_interval:
                try:
                    results[name] = {
                        "status": "healthy" if check_func() else "unhealthy",
                        "timestamp": current_time,
                        "error": None
                    }
                except Exception as e:
                    results[name] = {
                        "status": "error",
                        "timestamp": current_time,
                        "error": str(e)
                    }
                    
                    error_tracker.record_error(
                        e, ErrorSeverity.HIGH, ErrorCategory.SYSTEM,
                        {"health_check": name}
                    )
                
                self.last_check_time[name] = current_time
        
        return results

# Global health checker
health_checker = HealthChecker()

# Register default health checks
def redis_health_check() -> bool:
    """Check Redis connectivity"""
    try:
        redis_client = redis.Redis.from_url(config.get_redis_url())
        redis_client.ping()
        return True
    except:
        return False

health_checker.register_check("redis", redis_health_check)
