#!/bin/bash

# Enhanced YOLO Tracking System Startup Script
# This script provides easy startup options for different deployment scenarios

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
ENVIRONMENT="development"
COMPONENTS="all"
USE_DOCKER=false
INSTALL_DEPS=false

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "  Enhanced YOLO Tracking System"
    echo "  Startup Script"
    echo "=================================================="
    echo -e "${NC}"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -e, --env ENVIRONMENT     Set environment (development/production/testing)"
    echo "  -c, --components LIST     Components to start (redis,celery_worker,celery_beat,flower,gui)"
    echo "  -d, --docker             Use Docker deployment"
    echo "  -i, --install            Install dependencies first"
    echo "  -h, --help               Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                                    # Start all components in development mode"
    echo "  $0 -e production -d                  # Start in production mode with Docker"
    echo "  $0 -c redis,celery_worker,gui        # Start only specific components"
    echo "  $0 -i                                # Install dependencies and start"
}

# Function to check dependencies
check_dependencies() {
    print_status "Checking dependencies..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is required but not installed"
        exit 1
    fi
    
    # Check pip
    if ! command -v pip3 &> /dev/null; then
        print_error "pip3 is required but not installed"
        exit 1
    fi
    
    # Check Redis (if not using Docker)
    if [ "$USE_DOCKER" = false ]; then
        if ! command -v redis-server &> /dev/null; then
            print_warning "Redis server not found. Will attempt to start with Docker."
            USE_DOCKER=true
        fi
    fi
    
    # Check Docker (if using Docker)
    if [ "$USE_DOCKER" = true ]; then
        if ! command -v docker &> /dev/null; then
            print_error "Docker is required but not installed"
            exit 1
        fi
        
        if ! command -v docker-compose &> /dev/null; then
            print_error "Docker Compose is required but not installed"
            exit 1
        fi
    fi
    
    print_status "Dependencies check completed"
}

# Function to install Python dependencies
install_dependencies() {
    print_status "Installing Python dependencies..."
    
    if [ -f "requirements.txt" ]; then
        pip3 install -r requirements.txt
        print_status "Python dependencies installed"
    else
        print_error "requirements.txt not found"
        exit 1
    fi
}

# Function to create necessary directories
create_directories() {
    print_status "Creating necessary directories..."
    
    mkdir -p data
    mkdir -p temp
    mkdir -p logs
    
    print_status "Directories created"
}

# Function to start with Docker
start_with_docker() {
    print_status "Starting system with Docker..."
    
    # Set environment variables
    export YOLO_ENV=$ENVIRONMENT
    
    if [ "$COMPONENTS" = "all" ]; then
        docker-compose up -d
    else
        # Start specific services
        IFS=',' read -ra COMP_ARRAY <<< "$COMPONENTS"
        for comp in "${COMP_ARRAY[@]}"; do
            case $comp in
                "redis")
                    docker-compose up -d redis
                    ;;
                "celery_worker")
                    docker-compose up -d celery_worker
                    ;;
                "celery_beat")
                    docker-compose up -d celery_beat
                    ;;
                "flower")
                    docker-compose up -d flower
                    ;;
                "gui")
                    docker-compose up -d gui_app
                    ;;
                *)
                    print_warning "Unknown component: $comp"
                    ;;
            esac
        done
    fi
    
    print_status "Docker containers started"
    print_status "Access Flower monitor at: http://localhost:5555"
    
    # Show logs
    echo ""
    print_status "Showing container logs (Ctrl+C to stop):"
    docker-compose logs -f
}

# Function to start without Docker
start_without_docker() {
    print_status "Starting system without Docker..."
    
    # Set environment variables
    export YOLO_ENV=$ENVIRONMENT
    
    # Start the system using Python script
    if [ "$COMPONENTS" = "all" ]; then
        python3 start_system.py --env $ENVIRONMENT
    else
        python3 start_system.py --env $ENVIRONMENT --components ${COMPONENTS//,/ }
    fi
}

# Function to stop the system
stop_system() {
    print_status "Stopping system..."
    
    if [ "$USE_DOCKER" = true ]; then
        docker-compose down
    else
        # Kill Python processes
        pkill -f "celery.*worker" || true
        pkill -f "celery.*beat" || true
        pkill -f "celery.*flower" || true
        pkill -f "enhanced_gui_analyzer.py" || true
    fi
    
    print_status "System stopped"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        -c|--components)
            COMPONENTS="$2"
            shift 2
            ;;
        -d|--docker)
            USE_DOCKER=true
            shift
            ;;
        -i|--install)
            INSTALL_DEPS=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        --stop)
            stop_system
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Main execution
main() {
    print_header
    
    print_status "Environment: $ENVIRONMENT"
    print_status "Components: $COMPONENTS"
    print_status "Use Docker: $USE_DOCKER"
    
    # Check dependencies
    check_dependencies
    
    # Install dependencies if requested
    if [ "$INSTALL_DEPS" = true ]; then
        install_dependencies
    fi
    
    # Create directories
    create_directories
    
    # Start the system
    if [ "$USE_DOCKER" = true ]; then
        start_with_docker
    else
        start_without_docker
    fi
}

# Handle Ctrl+C
trap 'print_status "Received interrupt signal"; stop_system; exit 0' INT

# Run main function
main
